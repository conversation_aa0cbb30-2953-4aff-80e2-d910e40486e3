"""
Entity Extractor - Extracts functions, classes, and variables from ASTs.

This module analyzes ASTs to extract detailed information about code entities,
including their signatures, documentation, and structural properties.
"""

import ast
from typing import Dict, List, Any, Optional

from .ir_context import IRContext, EntityInfo, ParameterInfo, ReturnInfo, create_parameter_info, create_return_info


class EntityExtractor:
    """
    Extracts detailed information about code entities from ASTs.
    
    This analyzer walks through ASTs to identify and extract:
    - Functions with parameters, return types, and docstrings
    - Classes with methods and inheritance information
    - Variables and constants
    - Decorators and other metadata
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the entity extractor with configuration.
        
        Args:
            config: Configuration dictionary for extraction options
        """
        self.config = config
        self.verbose = config.get('verbose', False)
        self.extract_variables = config.get('extract_variables', True)
        self.extract_constants = config.get('extract_constants', True)
        self.min_function_lines = config.get('min_function_lines', 1)
    
    def extract(self, context: IRContext) -> IRContext:
        """
        Extract entities from all modules in the context.
        
        Args:
            context: The IR context containing parsed modules
            
        Returns:
            Updated context with extracted entities
        """
        total_modules = len(context.modules)
        
        for i, (module_name, module_info) in enumerate(context.modules.items(), 1):
            if self.verbose and i % 10 == 0:
                print(f"   Progress: {i}/{total_modules} modules ({i/total_modules*100:.1f}%)")
            
            try:
                entities = self._extract_from_module(module_info)
                module_info.entities = entities
                
            except Exception as e:
                if self.verbose:
                    print(f"   Warning: Failed to extract entities from {module_name}: {e}")
                continue
        
        if self.verbose:
            total_entities = sum(len(module.entities) for module in context.modules.values())
            print(f"   Extracted {total_entities} entities total")
        
        return context
    
    def _extract_from_module(self, module_info) -> List[EntityInfo]:
        """
        Extract entities from a single module.
        
        Args:
            module_info: ModuleInfo object with AST
            
        Returns:
            List of extracted EntityInfo objects
        """
        if not module_info.ast_tree:
            return []
        
        entities = []
        current_class = None

        # First pass: Extract top-level entities and track class context
        for node in ast.walk(module_info.ast_tree):
            if isinstance(node, ast.ClassDef):
                # Extract class entity
                class_entity = self._extract_class(node, module_info.source_code)
                if class_entity:
                    entities.append(class_entity)
                    current_class = class_entity.name

                    # Extract methods within this class
                    method_entities = self._extract_class_methods(node, current_class, module_info.source_code)
                    entities.extend(method_entities)

            elif isinstance(node, ast.FunctionDef):
                # Only extract if it's not inside a class (top-level function)
                if not self._is_inside_class(node, module_info.ast_tree):
                    entity = self._extract_function(node, module_info.source_code)
                    if entity:
                        entities.append(entity)

            elif isinstance(node, ast.AsyncFunctionDef):
                # Only extract if it's not inside a class (top-level async function)
                if not self._is_inside_class(node, module_info.ast_tree):
                    entity = self._extract_function(node, module_info.source_code, is_async=True)
                    if entity:
                        entities.append(entity)

            elif self.extract_variables and isinstance(node, ast.Assign):
                variables = self._extract_variables(node, module_info.source_code)
                entities.extend(variables)

        return entities
    
    def _extract_function(self, node: ast.FunctionDef, source_code: str, is_async: bool = False) -> Optional[EntityInfo]:
        """
        Extract detailed information from a function definition.
        
        Args:
            node: AST function definition node
            source_code: Source code for docstring extraction
            is_async: Whether this is an async function
            
        Returns:
            EntityInfo object or None
        """
        try:
            # Basic information
            func_name = node.name
            func_type = "async_function" if is_async else "function"
            
            # Extract docstring
            docstring = self._extract_docstring(node)
            
            # Extract parameters
            params = self._extract_parameters(node.args)
            
            # Extract return type
            returns = create_return_info(node.returns) if node.returns else None
            
            # Extract decorators
            decorators = [self._get_decorator_name(dec) for dec in node.decorator_list]
            
            # Calculate line numbers
            line_start = node.lineno
            line_end = getattr(node, 'end_lineno', None) or line_start
            
            # Skip very small functions if configured
            if line_end - line_start < self.min_function_lines:
                return None
            
            entity = EntityInfo(
                type=func_type,
                name=func_name,
                doc=docstring,
                params=params,
                returns=returns,
                decorators=decorators,
                line_start=line_start,
                line_end=line_end,
                ast_node=node
            )
            
            return entity
            
        except Exception as e:
            if self.verbose:
                print(f"   Warning: Failed to extract function {getattr(node, 'name', 'unknown')}: {e}")
            return None
    
    def _extract_class(self, node: ast.ClassDef, source_code: str) -> Optional[EntityInfo]:
        """
        Extract detailed information from a class definition.
        
        Args:
            node: AST class definition node
            source_code: Source code for analysis
            
        Returns:
            EntityInfo object or None
        """
        try:
            # Basic information
            class_name = node.name
            
            # Extract docstring
            docstring = self._extract_docstring(node)
            
            # Extract base classes
            base_classes = []
            for base in node.bases:
                if isinstance(base, ast.Name):
                    base_classes.append(base.id)
                elif isinstance(base, ast.Attribute):
                    base_classes.append(self._get_attribute_name(base))

            # Extract decorators
            decorators = [self._get_decorator_name(dec) for dec in node.decorator_list]

            # Check if class is abstract
            is_abstract = any(dec in ['abstractmethod', 'ABC'] for dec in decorators)

            # Calculate line numbers
            line_start = node.lineno
            line_end = getattr(node, 'end_lineno', None) or line_start

            # Create parameter info for base classes (inheritance as "parameters")
            params = [ParameterInfo(name=base, type_hint="base_class") for base in base_classes]

            entity = EntityInfo(
                type="class",
                name=class_name,
                doc=docstring,
                params=params,
                returns=ReturnInfo(type_hint="class_instance"),
                decorators=decorators,
                line_start=line_start,
                line_end=line_end,
                # NEW: Enhanced inheritance information
                inherits_from=base_classes,
                is_abstract=is_abstract,
                ast_node=node
            )
            
            return entity
            
        except Exception as e:
            if self.verbose:
                print(f"   Warning: Failed to extract class {getattr(node, 'name', 'unknown')}: {e}")
            return None
    
    def _extract_variables(self, node: ast.Assign, source_code: str) -> List[EntityInfo]:
        """
        Extract variable assignments.
        
        Args:
            node: AST assignment node
            source_code: Source code for analysis
            
        Returns:
            List of EntityInfo objects for variables
        """
        variables = []
        
        try:
            for target in node.targets:
                if isinstance(target, ast.Name):
                    var_name = target.id
                    
                    # Determine if it's a constant (uppercase name)
                    var_type = "constant" if var_name.isupper() else "variable"
                    
                    # Skip if not extracting this type
                    if var_type == "constant" and not self.extract_constants:
                        continue
                    if var_type == "variable" and not self.extract_variables:
                        continue
                    
                    entity = EntityInfo(
                        type=var_type,
                        name=var_name,
                        line_start=node.lineno,
                        line_end=getattr(node, 'end_lineno', None) or node.lineno,
                        ast_node=node
                    )
                    
                    variables.append(entity)
        
        except Exception:
            pass  # Skip problematic variable extractions
        
        return variables

    def _extract_class_methods(self, class_node: ast.ClassDef, class_name: str, source_code: str) -> List[EntityInfo]:
        """
        Extract methods from a class definition.

        Args:
            class_node: AST class definition node
            class_name: Name of the class
            source_code: Source code for analysis

        Returns:
            List of EntityInfo objects for methods
        """
        methods = []

        for node in class_node.body:
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                method_entity = self._extract_method(node, class_name, source_code,
                                                   is_async=isinstance(node, ast.AsyncFunctionDef))
                if method_entity:
                    methods.append(method_entity)

        return methods

    def _extract_method(self, node: ast.FunctionDef, class_name: str, source_code: str, is_async: bool = False) -> Optional[EntityInfo]:
        """
        Extract detailed information from a method definition.

        Args:
            node: AST function definition node
            class_name: Name of the containing class
            source_code: Source code for analysis
            is_async: Whether this is an async method

        Returns:
            EntityInfo object or None
        """
        try:
            # Basic information
            method_name = node.name
            method_type = "method"

            # Extract docstring
            docstring = self._extract_docstring(node)

            # Extract parameters
            params = self._extract_parameters(node.args)

            # Extract return type
            returns = create_return_info(node.returns) if node.returns else None

            # Extract decorators
            decorators = [self._get_decorator_name(dec) for dec in node.decorator_list]

            # Determine method characteristics
            is_property = 'property' in decorators
            is_abstract = 'abstractmethod' in decorators
            access_modifier = self._determine_access_modifier(method_name)

            # Check if method calls super()
            calls_super = self._calls_super(node, method_name)

            # Calculate line numbers
            line_start = node.lineno
            line_end = getattr(node, 'end_lineno', None) or line_start

            # Skip very small methods if configured
            if line_end - line_start < self.min_function_lines:
                return None

            entity = EntityInfo(
                type=method_type,
                name=method_name,
                doc=docstring,
                params=params,
                returns=returns,
                decorators=decorators,
                line_start=line_start,
                line_end=line_end,
                # NEW: Method-specific information
                class_name=class_name,
                calls_super=calls_super,
                is_abstract=is_abstract,
                is_property=is_property,
                access_modifier=access_modifier,
                ast_node=node
            )

            return entity

        except Exception as e:
            if self.verbose:
                print(f"   Warning: Failed to extract method {getattr(node, 'name', 'unknown')}: {e}")
            return None

    def _is_inside_class(self, func_node: ast.FunctionDef, module_ast: ast.AST) -> bool:
        """Check if a function is defined inside a class."""
        for node in ast.walk(module_ast):
            if isinstance(node, ast.ClassDef):
                for child in ast.walk(node):
                    if child is func_node:
                        return True
        return False

    def _calls_super(self, method_node: ast.FunctionDef, method_name: str) -> bool:
        """Check if a method calls super()."""
        for node in ast.walk(method_node):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name) and node.func.id == 'super':
                    return True
                elif isinstance(node.func, ast.Attribute):
                    if (isinstance(node.func.value, ast.Call) and
                        isinstance(node.func.value.func, ast.Name) and
                        node.func.value.func.id == 'super' and
                        node.func.attr == method_name):
                        return True
        return False

    def _determine_access_modifier(self, name: str) -> str:
        """Determine access modifier based on naming convention."""
        if name.startswith('__') and not name.endswith('__'):
            return "private"
        elif name.startswith('_'):
            return "protected"
        else:
            return "public"

    def _extract_parameters(self, args: ast.arguments) -> List[ParameterInfo]:
        """
        Extract parameter information from function arguments.
        
        Args:
            args: AST arguments object
            
        Returns:
            List of ParameterInfo objects
        """
        params = []
        
        # Regular arguments
        for i, arg in enumerate(args.args):
            # Skip 'self' and 'cls' parameters
            if arg.arg in ('self', 'cls'):
                continue
            
            # Get default value if available
            default = None
            defaults_offset = len(args.args) - len(args.defaults)
            if i >= defaults_offset:
                default = args.defaults[i - defaults_offset]
            
            param = create_parameter_info(arg.arg, arg.annotation, default)
            params.append(param)
        
        # Keyword-only arguments
        for i, arg in enumerate(args.kwonlyargs):
            default = args.kw_defaults[i] if i < len(args.kw_defaults) else None
            param = create_parameter_info(arg.arg, arg.annotation, default)
            params.append(param)
        
        return params
    
    def _extract_docstring(self, node) -> Optional[str]:
        """Extract docstring from a function or class node."""
        if (node.body and 
            isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            
            docstring = node.body[0].value.value.strip()
            # Return first line for brevity
            return docstring.split('\n')[0] if docstring else None
        
        return None
    
    def _get_decorator_name(self, decorator) -> str:
        """Get the name of a decorator."""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Attribute):
            return self._get_attribute_name(decorator)
        elif isinstance(decorator, ast.Call):
            return self._get_decorator_name(decorator.func)
        else:
            return str(decorator)
    
    def _get_attribute_name(self, node: ast.Attribute) -> str:
        """Get the full name of an attribute access."""
        if isinstance(node.value, ast.Name):
            return f"{node.value.id}.{node.attr}"
        elif isinstance(node.value, ast.Attribute):
            return f"{self._get_attribute_name(node.value)}.{node.attr}"
        else:
            return node.attr
