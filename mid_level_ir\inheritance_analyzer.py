"""
Inheritance Analyzer - Analyzes class hierarchies and method overrides.

This module builds comprehensive inheritance information by analyzing class relationships,
method overrides, and super() calls across the entire codebase.
"""

from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass
import ast

from .ir_context import IRContext, ModuleInfo, EntityInfo, ClassHierarchyInfo


class InheritanceAnalyzer:
    """
    Analyzes class inheritance relationships and method overrides.
    
    This analyzer builds a comprehensive view of class hierarchies, identifies
    method overrides, and tracks super() calls to provide rich inheritance context.
    """
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.class_map: Dict[str, EntityInfo] = {}  # class_name -> class entity
        self.method_map: Dict[str, List[EntityInfo]] = {}  # class_name -> list of methods
        
    def analyze(self, context: IRContext) -> IRContext:
        """
        Analyze inheritance relationships in the IR context.
        
        Args:
            context: The IR context to analyze
            
        Returns:
            Enhanced IR context with inheritance information
        """
        if self.verbose:
            print("🔗 Analyzing class inheritance relationships...")
        
        # Step 1: Build class and method maps
        self._build_class_maps(context)
        
        # Step 2: Analyze inheritance hierarchies
        self._analyze_hierarchies(context)
        
        # Step 3: Detect method overrides
        self._detect_method_overrides(context)
        
        # Step 4: Update entity inheritance information
        self._update_entity_inheritance_info(context)
        
        if self.verbose:
            total_classes = len(context.class_hierarchy)
            total_overrides = sum(len(info.overridden_methods) for info in context.class_hierarchy.values())
            print(f"   ✅ Analyzed {total_classes} class hierarchies")
            print(f"   ✅ Detected {total_overrides} method overrides")
        
        return context
    
    def _build_class_maps(self, context: IRContext) -> None:
        """Build maps of classes and their methods."""
        for module in context.modules.values():
            for entity in module.entities:
                if entity.type == "class":
                    self.class_map[entity.name] = entity
                    self.method_map[entity.name] = []
                elif entity.type == "method" and entity.class_name:
                    if entity.class_name not in self.method_map:
                        self.method_map[entity.class_name] = []
                    self.method_map[entity.class_name].append(entity)
    
    def _analyze_hierarchies(self, context: IRContext) -> None:
        """Analyze class inheritance hierarchies."""
        for class_name, class_entity in self.class_map.items():
            # Create class hierarchy info
            hierarchy_info = ClassHierarchyInfo(
                class_name=class_name,
                module_name=self._find_module_for_entity(context, class_entity),
                file_path=self._find_file_for_entity(context, class_entity),
                base_classes=class_entity.inherits_from.copy(),
                methods=[method.name for method in self.method_map.get(class_name, [])]
            )
            
            # Find derived classes
            hierarchy_info.derived_classes = self._find_derived_classes(class_name)
            
            # Add to context
            context.add_class_hierarchy(hierarchy_info)
    
    def _detect_method_overrides(self, context: IRContext) -> None:
        """Detect method overrides in class hierarchies."""
        for class_name, hierarchy_info in context.class_hierarchy.items():
            class_methods = self.method_map.get(class_name, [])
            
            for method in class_methods:
                # Check if this method overrides a base class method
                override_info = self._find_method_override(method, hierarchy_info.base_classes)
                if override_info:
                    # Update the method entity
                    method.overrides = override_info
                    
                    # Update the hierarchy info
                    hierarchy_info.overridden_methods[method.name] = override_info
                
                # Find which classes override this method
                overridden_by = self._find_classes_that_override(method.name, class_name)
                if overridden_by:
                    method.overridden_by = overridden_by
    
    def _find_method_override(self, method: EntityInfo, base_classes: List[str]) -> Optional[str]:
        """Find which base class method is overridden by the given method."""
        for base_class in base_classes:
            base_methods = self.method_map.get(base_class, [])
            for base_method in base_methods:
                if base_method.name == method.name:
                    return base_class
            
            # Check recursively in base class hierarchies
            base_class_entity = self.class_map.get(base_class)
            if base_class_entity and base_class_entity.inherits_from:
                recursive_override = self._find_method_override(method, base_class_entity.inherits_from)
                if recursive_override:
                    return recursive_override
        
        return None
    
    def _find_classes_that_override(self, method_name: str, class_name: str) -> List[str]:
        """Find which classes override the given method."""
        overriding_classes = []
        
        for other_class_name, other_class_entity in self.class_map.items():
            if class_name in other_class_entity.inherits_from:
                # This class inherits from our class
                other_methods = self.method_map.get(other_class_name, [])
                for other_method in other_methods:
                    if other_method.name == method_name:
                        overriding_classes.append(other_class_name)
                        break
        
        return overriding_classes
    
    def _find_derived_classes(self, class_name: str) -> List[str]:
        """Find all classes that inherit from the given class."""
        derived = []
        for other_class_name, other_class_entity in self.class_map.items():
            if class_name in other_class_entity.inherits_from:
                derived.append(other_class_name)
        return derived
    
    def _update_entity_inheritance_info(self, context: IRContext) -> None:
        """Update entity objects with inheritance information."""
        for module in context.modules.values():
            for entity in module.entities:
                if entity.type == "method" and entity.class_name:
                    hierarchy_info = context.get_class_hierarchy(entity.class_name)
                    if hierarchy_info:
                        # Update inherits_from for the method's class
                        entity.inherits_from = hierarchy_info.base_classes.copy()
    
    def _find_module_for_entity(self, context: IRContext, entity: EntityInfo) -> str:
        """Find which module contains the given entity."""
        for module_name, module in context.modules.items():
            if entity in module.entities:
                return module_name
        return "unknown"
    
    def _find_file_for_entity(self, context: IRContext, entity: EntityInfo) -> str:
        """Find which file contains the given entity."""
        for module in context.modules.values():
            if entity in module.entities:
                return module.file
        return "unknown"


def create_inheritance_analyzer(verbose: bool = False) -> InheritanceAnalyzer:
    """Create a new inheritance analyzer instance."""
    return InheritanceAnalyzer(verbose=verbose)
