#!/usr/bin/env python3
"""
Final test of the complete inheritance analysis integration.
This test forces regeneration of IR with inheritance data and tests the LLM-friendly packages.
"""

import os
import sys
import time
import shutil

def test_complete_inheritance_integration():
    """Test the complete inheritance analysis integration end-to-end."""
    
    print("🎯 Final Inheritance Analysis Integration Test")
    print("=" * 80)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)
        
        # Step 1: Clear any cached IR data to force regeneration
        print("🗑️  Step 1: Clearing cached IR data...")
        cache_files = [
            "ir_cache.json",
            "project_ir_cache.json", 
            "aider__500_ir_cache.json"
        ]
        
        for cache_file in cache_files:
            if os.path.exists(cache_file):
                os.remove(cache_file)
                print(f"   Removed {cache_file}")
        
        # Step 2: Generate fresh IR with enhanced inheritance analysis
        print("\n🧬 Step 2: Generating fresh IR with inheritance analysis...")
        
        from mid_level_ir.main import MidLevelIRPipeline
        
        config = {
            'inheritance_analyzer': {'verbose': True},
            'entity_extractor': {'verbose': True},
            'verbose': True
        }
        
        pipeline = MidLevelIRPipeline(config)
        
        # Generate IR for the aider coders directory (has good class hierarchies)
        project_path = "aider-main/aider/coders"
        
        start_time = time.time()
        ir_data = pipeline.generate_ir(project_path, "enhanced_inheritance_ir.json")
        generation_time = time.time() - start_time
        
        print(f"✅ Enhanced IR generated in {generation_time:.2f}s")
        
        # Step 3: Analyze the generated IR for inheritance information
        print("\n🔍 Step 3: Analyzing generated IR for inheritance data...")
        
        inheritance_stats = {
            'classes_with_inheritance': 0,
            'methods_with_class_info': 0,
            'methods_with_overrides': 0,
            'methods_with_super_calls': 0,
            'abstract_methods': 0,
            'property_methods': 0
        }
        
        inheritance_examples = []
        
        for module in ir_data.get("modules", []):
            for entity in module.get("entities", []):
                if entity.get("type") == "class" and entity.get("inherits_from"):
                    inheritance_stats['classes_with_inheritance'] += 1
                    inheritance_examples.append(f"Class {entity['name']} inherits from {entity['inherits_from']}")
                
                elif entity.get("type") == "method":
                    if entity.get("class_name"):
                        inheritance_stats['methods_with_class_info'] += 1
                    
                    if entity.get("overrides"):
                        inheritance_stats['methods_with_overrides'] += 1
                        inheritance_examples.append(f"Method {entity['name']} overrides {entity['overrides']}")
                    
                    if entity.get("calls_super"):
                        inheritance_stats['methods_with_super_calls'] += 1
                        inheritance_examples.append(f"Method {entity['name']} calls super()")
                    
                    if entity.get("is_abstract"):
                        inheritance_stats['abstract_methods'] += 1
                    
                    if entity.get("is_property"):
                        inheritance_stats['property_methods'] += 1
        
        print(f"📊 Inheritance Analysis Results:")
        for key, value in inheritance_stats.items():
            print(f"   {key}: {value}")
        
        if inheritance_examples:
            print(f"\n🔍 Inheritance Examples (first 5):")
            for example in inheritance_examples[:5]:
                print(f"   • {example}")
        
        # Step 4: Test LLM-friendly package generation with the enhanced IR
        print("\n🤖 Step 4: Testing LLM-friendly package generation...")
        
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        # Create handler with the enhanced IR data directory
        handler = ContextRequestHandler(project_path)
        
        # Force use of the newly generated IR by updating the cache
        handler._ir_cache = ir_data
        
        enhanced_request = IRContextRequest(
            user_query="Show me class inheritance patterns and method overrides in the coder classes",
            task_description="Analyze inheritance relationships in coder classes",
            task_type="inheritance_analysis",
            focus_entities=["BaseCoder", "Coder", "method", "override"],
            max_tokens=2000,
            include_ir_slices=True,
            include_code_context=True,
            llm_friendly=True,
            max_output_chars=30000,
            max_entities=8
        )
        
        start_time = time.time()
        result = handler.process_ir_context_request(enhanced_request)
        processing_time = time.time() - start_time
        
        print(f"✅ LLM-friendly package generated in {processing_time:.2f}s")
        
        # Step 5: Analyze the LLM-friendly package for inheritance content
        print("\n📋 Step 5: Analyzing LLM-friendly package content...")
        
        if "llm_friendly_package" in result:
            package_content = result["llm_friendly_package"]
            package_size = result["package_size_chars"]
            
            # Check for enhanced inheritance indicators
            enhanced_indicators = [
                "Belongs to Class:",
                "Inherits From:",
                "🔁 Inheritance Context",
                "overrides",
                "super()",
                "calls_super",
                "overridden_by",
                "Abstract method",
                "Property method",
                "Access:"
            ]
            
            found_indicators = []
            for indicator in enhanced_indicators:
                if indicator in package_content:
                    found_indicators.append(indicator)
            
            print(f"📦 Package Analysis:")
            print(f"   Size: {package_size:,} characters")
            print(f"   Inheritance indicators: {len(found_indicators)}/{len(enhanced_indicators)}")
            
            for indicator in found_indicators:
                print(f"   ✅ {indicator}")
            
            # Save the final enhanced package
            with open("final_enhanced_inheritance_package.txt", "w", encoding="utf-8") as f:
                f.write(package_content)
            print(f"\n💾 Final package saved to: final_enhanced_inheritance_package.txt")
            
            # Determine success level
            if len(found_indicators) >= 8:
                return "excellent"
            elif len(found_indicators) >= 5:
                return "good"
            elif len(found_indicators) >= 3:
                return "partial"
            else:
                return "minimal"
        else:
            print("❌ LLM-friendly package was not generated!")
            return "failure"
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return "failure"

if __name__ == "__main__":
    print("🧪 Final Inheritance Analysis Integration Test")
    print("=" * 80)
    
    result = test_complete_inheritance_integration()
    
    print(f"\n🎯 Final Result: {result}")
    
    if result == "excellent":
        print("🎉 EXCELLENT! Complete inheritance analysis is working perfectly!")
        print("✅ All inheritance features are captured and displayed in LLM packages")
    elif result == "good":
        print("✅ GOOD! Most inheritance features are working correctly")
        print("💡 Check final_enhanced_inheritance_package.txt for details")
    elif result == "partial":
        print("⚠️  PARTIAL: Some inheritance features are working")
        print("💡 More work needed to capture all inheritance relationships")
    elif result == "minimal":
        print("⚠️  MINIMAL: Basic inheritance detection is working")
        print("💡 Significant work needed for complete inheritance analysis")
    else:
        print("❌ FAILURE: Inheritance analysis is not working")
        print("💡 Check the error messages above for debugging")
    
    sys.exit(0 if result in ["excellent", "good", "partial"] else 1)
