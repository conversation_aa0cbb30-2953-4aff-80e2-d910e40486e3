# USER QUERY
How does the intelligent context selection algorithm work? I need to understand the implementation.

# INTELLIGENT CONTEXT ANALYSIS PACKAGE
# Generated by IR_CONTEXT_REQUEST system

## TASK INFORMATION
- User Query: How does the intelligent context selection algorithm work? I need to understand the implementation.
- Task Description: How does the intelligent context selection algorithm work? I need to understand the implementation.
- Task Type: general_analysis

## CONTEXT SUMMARY
- Total Entities Selected: 89
- Total Tokens Used: 5997
- Critical Entities: 89
- High Priority Entities: 0
- Files Involved: 49
- Token Utilization: 100.0%

## SELECTION RATIONALE
Context Selection Rationale for general_analysis:

Selected 89 entities using 5997 tokens (100.0% of budget).

Priority Distribution:
  - critical: 89 entities

Criticality Distribution:
  - low: 1 entities
  - medium: 82 entities
  - high: 6 entities

Selection Strategy:
- Prioritized entities with high relevance scores
- Included critical dependencies and reverse dependencies
- Optimized for general_analysis task requirements
- Maintained token budget constraints

## IR ANALYSIS DATA (89 entities)

### 1. parse_context_request (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: high
- **Change Risk**: medium
- **Relevance Score**: 2.83974358974359
- **Priority**: critical
- **Calls**: search, strip, group, rstrip, startswith, endswith, replace, loads, sub, get, append, SymbolRequest, ContextRequest
- **Used By**: aider_context_request_integration, test_context_request_format_fix, test_aider_context_request_integration, test_context_request_missing_reason, test_directory_file_format, test_json_parsing_fix
- **Side Effects**: network_io, writes_log
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 2. process_context_requests (function)
- **Module**: base_coder
- **File**: aider-main\aider\coders\base_coder.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 2.7911538461538465
- **Priority**: critical
- **Calls**: tool_warning, getcwd, basename, exists, join, find_common_root, AiderContextRequestIntegration, tool_error, detect_context_request, tool_output, get_context_request_summary, get_repo_overview, callable, get_all_files, keys
- **Used By**: test_context_request_integration, test_context_request_hang, test_aider_coder_path_fix, test_repo_map_compatibility, test_context_request_fix, test_full_aider_integration, test_llm_workflow_understanding, test_context_request_code_block_fix, test_aider_context_request
- **Side Effects**: modifies_state, network_io, writes_log, database_io
- **Potential Errors**: TypeError, IndexError, AttributeError, ImportError, KeyError

### 3. process_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.773076923076923
- **Priority**: critical
- **Calls**: process_context_request, get, render_augmented_prompt
- **Used By**: test_augmented_prompt_content, test_context_request_real, test_surgical_integration, test_context_request_with_repo_map, test_query_distinction, base_coder_old, test_context_request_hang, test_complete_function_extraction, context_request_demo, test_surgical_extraction_integration
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, KeyError

### 4. detect_context_request (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.748076923076923
- **Priority**: critical
- **Calls**: parse_context_request, join
- **Used By**: base_coder, test_context_request, test_context_request_integration, base_coder_old, test_context_request_hang, test_full_aider_integration, test_repo_map_compatibility, context_request_demo, test_aider_context_request
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, KeyError

### 5. process_context_request (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 2.723076923076923
- **Priority**: critical
- **Calls**: join, _get_from_cache, _extract_symbol_content, _extract_essential_imports, SymbolInfo, _extract_containing_class, extract_usage_contexts, append, _update_cache
- **Used By**: test_augmented_prompt_content, test_context_request_real, test_surgical_integration, test_context_request_with_repo_map, test_query_distinction, base_coder_old, test_context_request_hang, test_complete_function_extraction, context_request_demo, test_surgical_extraction_integration
- **Side Effects**: modifies_state, network_io, writes_log, database_io
- **Potential Errors**: TypeError, IndexError, AttributeError, ImportError, KeyError

### 6. update_conversation_history (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.706923076923077
- **Priority**: critical
- **Calls**: get, append
- **Used By**: test_aider_context_request_integration, test_context_request_integration, test_repo_map_compatibility, test_context_request_hang, test_full_aider_integration, test_conversation_history_fix, test_context_request_fix
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 7. process_context_requests (function)
- **Module**: base_coder_old
- **File**: aider-main\aider\coders\base_coder_old.py
- **Criticality**: high
- **Change Risk**: high
- **Relevance Score**: 2.6961538461538463
- **Priority**: critical
- **Calls**: tool_warning, AiderContextRequestIntegration, tool_error, detect_context_request, tool_output, get_context_request_summary, get_repo_overview, callable, get_all_files, join, keys, sub, process_context_request
- **Used By**: test_context_request_integration, test_context_request_hang, test_aider_coder_path_fix, test_repo_map_compatibility, test_context_request_fix, test_full_aider_integration, test_llm_workflow_understanding, test_context_request_code_block_fix, test_aider_context_request
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: ImportError, KeyError, TypeError, AttributeError

### 8. _read_file_content (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.6535897435897438
- **Priority**: critical
- **Calls**: join, read_text
- **Used By**: enhanced_surgical_extractor, surgical_context_extractor, surgical_file_extractor
- **Side Effects**: modifies_file, modifies_state
- **Potential Errors**: KeyError, TypeError, IndexError, AttributeError

### 9. _extract_code_snippet (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.6235897435897435
- **Priority**: critical
- **Calls**: _read_file_content, splitlines, search, _find_complete_class_body, _find_complete_function_body, _find_surrounding_function, join, CodeSnippet
- **Used By**: test_surgical_context_extractor, simple_demo, surgical_context_extractor
- **Side Effects**: modifies_file, modifies_state
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, AttributeError, ImportError, KeyError

### 10. AiderContextRequestIntegration (class)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.61025641025641
- **Priority**: critical
- **Calls**: None
- **Used By**: test_augmented_prompt_content, base_coder, test_context_request_real, test_context_request_availability, test_surgical_integration, test_context_request_with_repo_map, base_coder_old, test_context_request_hang, test_conversation_history_fix, test_complete_function_extraction
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 11. SymbolRequest (class)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.61025641025641
- **Priority**: critical
- **Calls**: None
- **Used By**: test_augmented_prompt_content, test_context_request_real, test_context_request_availability, test_surgical_integration, test_context_request_with_repo_map, test_query_distinction, test_real_context_request_fix, test_context_request_path_fix, test_complete_function_extraction, context_request_demo
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 12. ContextRequest (class)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.61025641025641
- **Priority**: critical
- **Calls**: None
- **Used By**: test_augmented_prompt_content, test_context_request_real, test_context_request_availability, test_surgical_integration, test_context_request_with_repo_map, test_query_distinction, test_complete_function_extraction, context_request_demo, test_surgical_extraction_integration, test_no_dependencies
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 13. ContextRequestHandler (class)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.61025641025641
- **Priority**: critical
- **Calls**: None
- **Used By**: base_coder, test_no_dependencies, test_json_parsing_fix, test_ir_context_request, test_context_request_end_to_end, test_context_request_root_fix, aider_context_request_integration, test_aider_context_request_integration, test_context_request_format_fix, test_query_distinction
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 14. select_optimal_context (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.605
- **Priority**: critical
- **Calls**: _score_entities_for_task, _select_entities_within_budget, _enhance_with_dependency_context, _build_context_bundle, get_critical_entities
- **Used By**: intelligent_context_selector, iterative_analysis_engine, aider_integration_service
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, KeyError

### 15. _get_context_selector (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5911538461538464
- **Priority**: critical
- **Calls**: generate_mid_level_ir, IntelligentContextSelector
- **Used By**: test_intelligent_context_selection, aider_integration_service
- **Side Effects**: modifies_file, modifies_state, writes_log
- **Potential Errors**: ImportError, KeyError, TypeError, ValueError, AttributeError

### 16. test_all_possible_context_leaks (function)
- **Module**: test_hidden_context_detection
- **File**: test_hidden_context_detection.py
- **Criticality**: high
- **Change Risk**: medium
- **Relevance Score**: 2.58974358974359
- **Priority**: critical
- **Calls**: Model, InputOutput, GitRepo, create, fmt_system_prompt, get_repo_map, items, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 17. get_context_request_summary (function)
- **Module**: aider_context_request_integration
- **File**: aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: low
- **Relevance Score**: 2.586410256410256
- **Priority**: critical
- **Calls**: join
- **Used By**: base_coder, test_context_request, test_context_request_availability, test_aider_context_request_integration, test_context_request_integration, base_coder_old, test_context_request_hang, test_full_aider_integration, test_repo_map_compatibility, context_request_demo
- **Side Effects**: none
- **Potential Errors**: KeyError, TypeError, IndexError, AttributeError

### 18. select_intelligent_context (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.582307692307692
- **Priority**: critical
- **Calls**: _get_context_selector, get, lower, select_optimal_context, analyze_context_quality, append, sort
- **Used By**: test_intelligent_context_selection, aider_integration_service
- **Side Effects**: modifies_state, network_io, writes_log
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 19. analyze_context_quality (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5799999999999996
- **Priority**: critical
- **Calls**: get
- **Used By**: intelligent_context_selector, aider_integration_service
- **Side Effects**: network_io, modifies_container, modifies_state
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, AttributeError, KeyError

### 20. cmd_copy_context (function)
- **Module**: commands
- **File**: aider-main\aider\commands.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.566153846153846
- **Priority**: critical
- **Calls**: format_chat_chunks, get, copy, tool_output, tool_error
- **Used By**: base_coder_old, base_coder
- **Side Effects**: modifies_state, network_io, writes_log
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, AttributeError, KeyError

### 21. extract_usage_contexts (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.561923076923077
- **Priority**: critical
- **Calls**: _get_from_cache, get_files_that_import, append, _find_symbol_line_numbers, _read_file_content, _determine_usage_type, _determine_context_window_size, _extract_code_snippet, UsageContext, _update_cache
- **Used By**: enhanced_surgical_extractor, test_augmented_prompt_content, surgical_context_extractor, context_request_handler
- **Side Effects**: modifies_file, network_io, modifies_state, database_io
- **Potential Errors**: TypeError, IndexError, AttributeError, KeyError

### 22. main (function)
- **Module**: test_real_context_request_fix
- **File**: test_real_context_request_fix.py
- **Criticality**: high
- **Change Risk**: medium
- **Relevance Score**: 2.5535897435897437
- **Priority**: critical
- **Calls**: test_real_context_request_fix
- **Used By**: test_browser, test_ssl_verification, test_deprecated, test_main
- **Side Effects**: network_io, writes_log
- **Potential Errors**: ImportError, TypeError, ValueError, AttributeError

### 23. test_intelligent_context_integration (function)
- **Module**: test_intelligent_context_selection
- **File**: test_intelligent_context_selection.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5530769230769232
- **Priority**: critical
- **Calls**: AiderIntegrationService, getcwd, time, select_intelligent_context, append, _get_context_selector, get_entity_details, get_related_entities
- **Used By**: None
- **Side Effects**: modifies_file, writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, KeyError

### 24. test_context_request_clean_fix (function)
- **Module**: test_context_request_clean_fix
- **File**: test_context_request_clean_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.548076923076923
- **Priority**: critical
- **Calls**: search, strip, group, split, startswith, lower, MockCoder, _extract_actual_user_query, _contains_map_request_content, append, print_exc
- **Used By**: test_context_request_clean_fix
- **Side Effects**: writes_log, network_io, database_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 25. test_context_request_conversation_flow (function)
- **Module**: test_context_request_fix
- **File**: test_context_request_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.548076923076923
- **Priority**: critical
- **Calls**: MockIO, MockContextRequestIntegration, replace, MockCoder, process_context_requests, update_conversation_history, print_exc
- **Used By**: test_context_request_fix
- **Side Effects**: network_io, modifies_state, modifies_container, writes_log, database_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 26. test_context_request_path_resolution (function)
- **Module**: test_context_request_path_fix
- **File**: test_context_request_path_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.548076923076923
- **Priority**: critical
- **Calls**: TemporaryDirectory, join, makedirs, open, write, getcwd, chdir, insert, AiderIntegrationService, ContextRequestHandler, SymbolRequest, _find_file_for_symbol, _extract_symbol_content, print_exc
- **Used By**: test_context_request_path_fix
- **Side Effects**: modifies_file, modifies_state, network_io, writes_log, database_io
- **Potential Errors**: FileNotFoundError, PermissionError, ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 27. test_context_request_root_fix (function)
- **Module**: test_context_request_root_fix
- **File**: test_context_request_root_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.548076923076923
- **Priority**: critical
- **Calls**: TemporaryDirectory, join, makedirs, open, write, getcwd, chdir, insert, basename, exists, read, dirname, AiderIntegrationService, ContextRequestHandler, SymbolRequest
- **Used By**: test_context_request_root_fix
- **Side Effects**: modifies_file, modifies_state, network_io, writes_log, database_io
- **Potential Errors**: FileNotFoundError, PermissionError, ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 28. test_real_context_request_fix (function)
- **Module**: test_real_context_request_fix
- **File**: test_real_context_request_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.548076923076923
- **Priority**: critical
- **Calls**: TemporaryDirectory, join, makedirs, run, open, write, insert, dirname, InputOutput, GitRepo, Model, create, SymbolRequest, _find_file_for_symbol, print_exc
- **Used By**: test_real_context_request_fix
- **Side Effects**: modifies_file, modifies_state, network_io, writes_log, database_io
- **Potential Errors**: FileNotFoundError, PermissionError, ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 29. integrate_prioritization_with_algorithm (function)
- **Module**: fix_map_slicing
- **File**: fix_map_slicing.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.541153846153846
- **Priority**: critical
- **Calls**: Path, open, read, find, replace, write
- **Used By**: fix_map_slicing
- **Side Effects**: modifies_file, writes_log, modifies_state
- **Potential Errors**: FileNotFoundError, PermissionError, ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, KeyError

### 30. test_aider_coder_context_request_path (function)
- **Module**: test_aider_coder_path_fix
- **File**: test_aider_coder_path_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.541153846153846
- **Priority**: critical
- **Calls**: TemporaryDirectory, join, makedirs, open, write, getcwd, chdir, insert, Model, InputOutput, create, process_context_requests, strip, lower, print_exc
- **Used By**: test_aider_coder_path_fix
- **Side Effects**: modifies_file, writes_log, database_io, network_io
- **Potential Errors**: FileNotFoundError, PermissionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 31. test_partial_success_context_request (function)
- **Module**: test_partial_success_fix
- **File**: test_partial_success_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.541153846153846
- **Priority**: critical
- **Calls**: TemporaryDirectory, join, makedirs, run, open, write, insert, dirname, AiderContextRequestIntegration, SymbolRequest, ContextRequest, process_context_request, append, print_exc
- **Used By**: test_partial_success_fix
- **Side Effects**: modifies_file, modifies_state, network_io, writes_log, database_io
- **Potential Errors**: FileNotFoundError, PermissionError, ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 32. extract_dependency_contexts (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5319230769230767
- **Priority**: critical
- **Calls**: _get_from_cache, get_files_imported_by, get_files_that_import, get_symbol_references_between_files, items, _find_symbol_line_numbers, _read_file_content, _determine_context_window_size, _extract_code_snippet, append, _update_cache
- **Used By**: surgical_context_extractor
- **Side Effects**: modifies_file, network_io, modifies_state, database_io
- **Potential Errors**: TypeError, IndexError, AttributeError, ImportError, KeyError

### 33. test_aider_integration (function)
- **Module**: test_aider_context_request_integration
- **File**: test_aider_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5319230769230767
- **Priority**: critical
- **Calls**: getcwd, AiderIntegrationService, AiderContextRequestIntegration, get_llm_instructions, ContextRequest, SymbolRequest, get_context_request_summary, process_context_request, update_conversation_history, reset_iteration_counter
- **Used By**: test_aider_context_request_integration
- **Side Effects**: database_io, writes_log, network_io
- **Potential Errors**: KeyError, TypeError, IndexError, ValueError, AttributeError

### 34. test_markdown_stream_handling (function)
- **Module**: test_context_request_code_block_fix
- **File**: test_context_request_code_block_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5319230769230767
- **Priority**: critical
- **Calls**: open, read, find
- **Used By**: test_context_request_code_block_fix
- **Side Effects**: modifies_file, writes_log, modifies_state
- **Potential Errors**: FileNotFoundError, PermissionError, ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, KeyError

### 35. _update_cache (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5285897435897438
- **Priority**: critical
- **Calls**: time
- **Used By**: context_request_handler, surgical_file_extractor, models, test_model_info_manager, surgical_context_extractor, enhanced_surgical_extractor, openrouter
- **Side Effects**: modifies_state, modifies_container
- **Potential Errors**: KeyError, TypeError, IndexError, AttributeError

### 36. process_context_requests (function)
- **Module**: test_aider_context_request
- **File**: test_aider_context_request.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.528076923076923
- **Priority**: critical
- **Calls**: AiderContextRequestIntegration, tool_error, detect_context_request, process_context_request, sub
- **Used By**: test_context_request_integration, test_context_request_hang, test_aider_coder_path_fix, test_repo_map_compatibility, test_context_request_fix, test_full_aider_integration, test_llm_workflow_understanding, test_context_request_code_block_fix, test_aider_context_request
- **Side Effects**: modifies_state, writes_log, network_io
- **Potential Errors**: ImportError, KeyError, TypeError, AttributeError

### 37. process_context_requests (function)
- **Module**: test_context_request_hang
- **File**: test_context_request_hang.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.528076923076923
- **Priority**: critical
- **Calls**: tool_error, detect_context_request, tool_output, get_context_request_summary, process_context_request, update_conversation_history, sub
- **Used By**: test_context_request_integration, test_context_request_hang, test_aider_coder_path_fix, test_repo_map_compatibility, test_context_request_fix, test_full_aider_integration, test_llm_workflow_understanding, test_context_request_code_block_fix, test_aider_context_request
- **Side Effects**: database_io, writes_log, modifies_state, network_io
- **Potential Errors**: ImportError, KeyError, TypeError, AttributeError

### 38. tool_output (function)
- **Module**: test_context_request_hang
- **File**: test_context_request_hang.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5235897435897434
- **Priority**: critical
- **Calls**: append
- **Used By**: base_coder, commands, gui, scrape, base_coder_old, onboarding, utils, test_context_request_hang, versioncheck, test_io
- **Side Effects**: writes_log, modifies_state
- **Potential Errors**: KeyError, TypeError, IndexError, ValueError, AttributeError

### 39. test_context_bundle_builder (function)
- **Module**: test_context_bundle_builder
- **File**: test_context_bundle_builder.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.523076923076923
- **Priority**: critical
- **Calls**: AnalysisMemory, ConfidenceTracker, ContextBundleBuilder, time, build, to_dict, items, split, strip, _calculate_detailed_score, values, open, dump, print_exc
- **Used By**: None
- **Side Effects**: modifies_file, writes_log, modifies_container
- **Potential Errors**: FileNotFoundError, ZeroDivisionError, PermissionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 40. test_ir_context_request (function)
- **Module**: test_ir_context_request
- **File**: test_ir_context_request.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.523076923076923
- **Priority**: critical
- **Calls**: join, dirname, insert, getcwd, ContextRequestHandler, preload_ir_data, IRContextRequest, time, process_ir_context_request, get, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io, database_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 41. extract_enhanced_context (function)
- **Module**: enhanced_surgical_extractor
- **File**: enhanced_surgical_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.521153846153846
- **Priority**: critical
- **Calls**: _get_from_cache, get_symbols_in_file, next, extract_symbol_range, extract_symbol_content, _extract_essential_imports, _extract_containing_class, extract_usage_contexts, EnhancedCodeContext, _update_cache
- **Used By**: simple_enhanced_test, enhanced_extraction_demo
- **Side Effects**: network_io, modifies_state, database_io
- **Potential Errors**: KeyError, TypeError, IndexError, AttributeError

### 42. process_context_requests (function)
- **Module**: test_repo_map_compatibility
- **File**: test_repo_map_compatibility.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.521153846153846
- **Priority**: critical
- **Calls**: tool_error, detect_context_request, tool_output, get_context_request_summary, get_repo_overview, callable, get_all_files, join, tool_warning, keys, process_context_request, update_conversation_history, sub
- **Used By**: test_context_request_integration, test_context_request_hang, test_aider_coder_path_fix, test_repo_map_compatibility, test_context_request_fix, test_full_aider_integration, test_llm_workflow_understanding, test_context_request_code_block_fix, test_aider_context_request
- **Side Effects**: database_io, writes_log, modifies_state, network_io
- **Potential Errors**: ImportError, KeyError, TypeError, AttributeError

### 43. process_ir_context_requests (function)
- **Module**: base_coder
- **File**: aider-main\aider\coders\base_coder.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.516153846153846
- **Priority**: critical
- **Calls**: tool_warning, search, strip, group, loads, IRContextRequest, get, tool_output, getcwd, basename, exists, join, find_common_root, ContextRequestHandler, tool_error
- **Used By**: None
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: TypeError, IndexError, AttributeError, ImportError, KeyError

### 44. _extract_definition_info (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.51525641025641
- **Priority**: critical
- **Calls**: splitlines, search, escape, strip, _extract_docstring, startswith, endswith, isupper
- **Used By**: test_surgical_context_extractor, surgical_context_extractor
- **Side Effects**: modifies_state
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, AttributeError, KeyError

### 45. debug_context_request_paths (function)
- **Module**: debug_context_request_path
- **File**: debug_context_request_path.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5147435897435897
- **Priority**: critical
- **Calls**: getcwd, ContextRequestHandler, SymbolRequest, join, exists, dirname, listdir, isdir, walk, append, lower, endswith, relpath, _find_file_for_symbol, print_exc
- **Used By**: debug_context_request_path
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 46. test_context_request_code_block_closure (function)
- **Module**: test_context_request_code_block_fix
- **File**: test_context_request_code_block_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5147435897435897
- **Priority**: critical
- **Calls**: Model, InputOutput, create, process_context_requests, print_exc
- **Used By**: test_context_request_code_block_fix
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ImportError, KeyError, TypeError, IndexError, ValueError, AttributeError

### 47. get_focused_inheritance_context (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5128205128205128
- **Priority**: critical
- **Calls**: _get_context_extractor, get_focused_inheritance_context, append
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: KeyError, TypeError, IndexError, AttributeError

### 48. test_context_request_end_to_end (function)
- **Module**: test_context_request_end_to_end
- **File**: test_context_request_end_to_end.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5128205128205128
- **Priority**: critical
- **Calls**: create_mock_trading_project, AiderIntegrationService, ContextRequestHandler, ContextRequest, SymbolRequest, process_context_request, print_exc, exists, rmtree
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 49. extract_definition_contexts (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5119230769230767
- **Priority**: critical
- **Calls**: join, _get_from_cache, find_file_defining_symbol, _find_definition_line_numbers, _read_file_content, _extract_definition_info, _determine_context_window_size, _extract_code_snippet, DefinitionContext, append, _update_cache
- **Used By**: surgical_context_extractor
- **Side Effects**: modifies_file, modifies_state, network_io, writes_log, database_io
- **Potential Errors**: TypeError, IndexError, AttributeError, KeyError

### 50. send_message (function)
- **Module**: test_context_request_hang
- **File**: test_context_request_hang.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5119230769230767
- **Priority**: critical
- **Calls**: process_context_requests, tool_output, sleep, update_conversation_history
- **Used By**: base_coder, base_coder_old, test_map_request_flow, test_coder, test_context_request_hang
- **Side Effects**: network_io, modifies_state, database_io
- **Potential Errors**: ImportError, KeyError, IndexError, AttributeError

### 51. items (variable)
- **Module**: debug_context_request_path
- **File**: debug_context_request_path.py
- **Criticality**: low
- **Change Risk**: medium
- **Relevance Score**: 2.5102564102564098
- **Priority**: critical
- **Calls**: None
- **Used By**: base_coder, search_replace, architecture_diagram_generator, blame, commands, test_focused_map, test_external_project, surgical_context_extractor, utils, base_coder_old
- **Side Effects**: none
- **Potential Errors**: RuntimeError

### 52. process_context_requests (function)
- **Module**: test_context_request_integration
- **File**: test_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.508076923076923
- **Priority**: critical
- **Calls**: tool_error, detect_context_request, tool_output, get_context_request_summary, process_context_request, update_conversation_history, sub
- **Used By**: test_context_request_integration, test_context_request_hang, test_aider_coder_path_fix, test_repo_map_compatibility, test_context_request_fix, test_full_aider_integration, test_llm_workflow_understanding, test_context_request_code_block_fix, test_aider_context_request
- **Side Effects**: database_io, writes_log, modifies_state, network_io
- **Potential Errors**: TypeError, KeyError, AttributeError

### 53. build (function)
- **Module**: context_bundle_builder
- **File**: context_bundle_builder.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.506923076923077
- **Priority**: critical
- **Calls**: _score_all_entities, _select_entities_within_budget, _build_enhanced_bundle
- **Used By**: test_context_bundle_builder, iterative_analysis_engine, main
- **Side Effects**: writes_log, modifies_state, network_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, KeyError

### 54. test_message_construction_deep_dive (function)
- **Module**: test_hidden_context_detection
- **File**: test_hidden_context_detection.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.506923076923077
- **Priority**: critical
- **Calls**: Model, InputOutput, GitRepo, create, get_system_message, get_repo_messages, get_readonly_files_messages, method_call, print_exc
- **Used By**: None
- **Side Effects**: modifies_file, writes_log, network_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 55. _update_cache (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5035897435897434
- **Priority**: critical
- **Calls**: time
- **Used By**: context_request_handler, surgical_file_extractor, models, test_model_info_manager, surgical_context_extractor, enhanced_surgical_extractor, openrouter
- **Side Effects**: modifies_state, modifies_container
- **Potential Errors**: KeyError, TypeError, IndexError, AttributeError

### 56. get_focused_inheritance_context (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.503076923076923
- **Priority**: critical
- **Calls**: get_base_classes_of, get_derived_classes_of, get, _find_definition_line_numbers, _read_file_content, _extract_definition_info, _determine_context_window_size, _extract_code_snippet, DefinitionContext, append, splitlines, match, group, startswith, search
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: modifies_file, network_io, writes_log, modifies_state
- **Potential Errors**: TypeError, IndexError, AttributeError, KeyError

### 57. _enhance_with_dependency_context (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.5016666666666665
- **Priority**: critical
- **Calls**: copy, get, append, add
- **Used By**: intelligent_context_selector
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: TypeError, IndexError, AttributeError, KeyError

### 58. process_context_requests (function)
- **Module**: test_full_aider_integration
- **File**: test_full_aider_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.501153846153846
- **Priority**: critical
- **Calls**: AiderContextRequestIntegration, tool_error, detect_context_request, tool_output, get_context_request_summary, get_repo_overview, process_context_request, update_conversation_history, sub
- **Used By**: test_context_request_integration, test_context_request_hang, test_aider_coder_path_fix, test_repo_map_compatibility, test_context_request_fix, test_full_aider_integration, test_llm_workflow_understanding, test_context_request_code_block_fix, test_aider_context_request
- **Side Effects**: network_io, modifies_state, writes_log, database_io
- **Potential Errors**: TypeError, KeyError, AttributeError

### 59. _display_ir_context_response_to_user (function)
- **Module**: base_coder
- **File**: aider-main\aider\coders\base_coder.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.498974358974359
- **Priority**: critical
- **Calls**: join, get, dumps
- **Used By**: None
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, AttributeError, KeyError

### 60. _get_from_cache (function)
- **Module**: context_request_handler
- **File**: context_request_handler.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4985897435897435
- **Priority**: critical
- **Calls**: get, time
- **Used By**: enhanced_surgical_extractor, surgical_context_extractor, context_request_handler, surgical_file_extractor
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: TypeError, IndexError, AttributeError, ImportError, KeyError

### 61. check_environment_setup (function)
- **Module**: debug_context_request_path
- **File**: debug_context_request_path.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4985897435897435
- **Priority**: critical
- **Calls**: getcwd, walk, append, relpath
- **Used By**: debug_context_request_path
- **Side Effects**: writes_log, network_io
- **Potential Errors**: KeyError, TypeError, IndexError, ValueError, AttributeError

### 62. print_formatted_results (function)
- **Module**: demo_intelligent_code_discovery
- **File**: demo_intelligent_code_discovery.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4985897435897435
- **Priority**: critical
- **Calls**: get, title, split, strip, items, join
- **Used By**: demo_intelligent_code_discovery
- **Side Effects**: network_io, writes_log
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, KeyError

### 63. tool_output (function)
- **Module**: test_context_request_integration
- **File**: test_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4985897435897435
- **Priority**: critical
- **Calls**: append
- **Used By**: base_coder, commands, gui, scrape, base_coder_old, onboarding, utils, test_context_request_hang, versioncheck, test_io
- **Side Effects**: writes_log, modifies_state
- **Potential Errors**: KeyError, TypeError, IndexError, ValueError, AttributeError

### 64. test_repo_messages_structure (function)
- **Module**: test_context_request_priority_fix
- **File**: test_context_request_priority_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4985897435897435
- **Priority**: critical
- **Calls**: Model, InputOutput, create, get_repo_messages, print_exc
- **Used By**: test_context_request_priority_fix
- **Side Effects**: network_io, writes_log
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 65. print_discovery_results (function)
- **Module**: test_intelligent_code_discovery
- **File**: test_intelligent_code_discovery.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4985897435897435
- **Priority**: critical
- **Calls**: get, split, strip
- **Used By**: test_intelligent_code_discovery
- **Side Effects**: network_io, writes_log
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, KeyError

### 66. get_contextual_dependencies (function)
- **Module**: aider_integration_service
- **File**: aider_integration_service.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4966666666666666
- **Priority**: critical
- **Calls**: _get_context_extractor, get_contextual_dependencies, append
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: KeyError, TypeError, IndexError, AttributeError

### 67. tool_error (function)
- **Module**: test_context_request_hang
- **File**: test_context_request_hang.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4902564102564098
- **Priority**: critical
- **Calls**: append
- **Used By**: base_coder, repo, io, smart_map_request_handler, commands, gui, scrape, test_context_request_integration, onboarding, base_coder_old
- **Side Effects**: writes_log
- **Potential Errors**: KeyError, TypeError, IndexError, ValueError, AttributeError

### 68. tool_error (function)
- **Module**: test_context_request_integration
- **File**: test_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4902564102564098
- **Priority**: critical
- **Calls**: append
- **Used By**: base_coder, repo, io, smart_map_request_handler, commands, gui, scrape, test_context_request_integration, onboarding, base_coder_old
- **Side Effects**: writes_log
- **Potential Errors**: KeyError, TypeError, IndexError, ValueError, AttributeError

### 69. test_context_request_parsing (function)
- **Module**: test_context_request_format_fix
- **File**: test_context_request_format_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.48974358974359
- **Priority**: critical
- **Calls**: ContextRequestHandler, parse_context_request, print_exc
- **Used By**: test_aider_context_request_integration
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 70. test_context_request_object_access (function)
- **Module**: test_context_request_object_fix
- **File**: test_context_request_object_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.48974358974359
- **Priority**: critical
- **Calls**: ContextRequest, SymbolRequest, append, get, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 71. test_partial_context_request (function)
- **Module**: test_partial_context_request
- **File**: test_partial_context_request.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.48974358974359
- **Priority**: critical
- **Calls**: ContextRequestHandler, ContextRequest, SymbolRequest, process_context_request, get, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 72. process_context_request (function)
- **Module**: test_augmented_prompt_content
- **File**: test_augmented_prompt_content.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.487820512820513
- **Priority**: critical
- **Calls**: split, find_file_defining_symbol, extract_symbol_content, extract_essential_imports, extract_containing_class, extract_usage_contexts, append, get
- **Used By**: test_augmented_prompt_content, test_context_request_real, test_surgical_integration, test_context_request_with_repo_map, test_query_distinction, base_coder_old, test_context_request_hang, test_complete_function_extraction, context_request_demo, test_surgical_extraction_integration
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: KeyError, IndexError, ValueError, AttributeError

### 73. get_related_entities (function)
- **Module**: intelligent_context_selector
- **File**: intelligent_context_selector.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4855128205128207
- **Priority**: critical
- **Calls**: add, get, explore_dependencies
- **Used By**: test_intelligent_context_selection
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: KeyError, TypeError, IndexError, AttributeError

### 74. debug_tree_context (function)
- **Module**: debug_repo_map
- **File**: debug_repo_map.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.482820512820513
- **Priority**: critical
- **Calls**: Model, InputOutput, RepoMap, render_tree, split, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 75. test_binary_search_algorithm_removal (function)
- **Module**: test_complete_repo_map_elimination
- **File**: test_complete_repo_map_elimination.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.482820512820513
- **Priority**: critical
- **Calls**: Model, InputOutput, RepoMap, get_ranked_tags_map, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 76. test_context_request_new_format (function)
- **Module**: test_directory_file_format
- **File**: test_directory_file_format.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.482820512820513
- **Priority**: critical
- **Calls**: ContextRequestHandler, strip, parse_context_request, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 77. test_context_request_error_handling (function)
- **Module**: test_error_handling_fix
- **File**: test_error_handling_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.482820512820513
- **Priority**: critical
- **Calls**: ContextRequestHandler, AiderTemplateRenderer, ContextRequest, SymbolRequest, process_context_request, get, render_augmented_prompt, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 78. test_integration_with_context_request (function)
- **Module**: test_repomap_style_file_discovery
- **File**: test_repomap_style_file_discovery.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.482820512820513
- **Priority**: critical
- **Calls**: AiderIntegrationService, ContextRequestHandler, SymbolRequest, _find_file_for_symbol, isabs, join, exists, _extract_symbol_content, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 79. test_no_slicing_algorithm_execution (function)
- **Module**: test_repository_map_slicing_termination
- **File**: test_repository_map_slicing_termination.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.482820512820513
- **Priority**: critical
- **Calls**: Model, InputOutput, RepoMap, get_repo_map, print_exc
- **Used By**: None
- **Side Effects**: writes_log, network_io
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, ValueError, RuntimeError, AttributeError, ImportError, KeyError

### 80. test_context_request_integration (function)
- **Module**: test_simplified_system
- **File**: test_simplified_system.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.482820512820513
- **Priority**: critical
- **Calls**: insert, join, dirname
- **Used By**: None
- **Side Effects**: writes_log, database_io
- **Potential Errors**: ImportError, KeyError, TypeError, ValueError, AttributeError

### 81. main (function)
- **Module**: context_request_demo
- **File**: context_request_demo.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: getcwd, AiderIntegrationService, AiderContextRequestIntegration, get_llm_instructions, ContextRequest, SymbolRequest, get_context_request_summary, process_context_request, detect_context_request
- **Used By**: test_browser, test_ssl_verification, test_deprecated, test_main
- **Side Effects**: network_io, writes_log
- **Potential Errors**: KeyError, TypeError, IndexError, ValueError, AttributeError

### 82. get_contextual_dependencies (function)
- **Module**: surgical_context_extractor
- **File**: surgical_context_extractor.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: extract_dependency_contexts, get_symbols_defined_in_file, items, extend, extract_usage_contexts, get_files_imported_by, get_symbol_references_between_files, extract_definition_contexts, sort, ContextualDependencyMap
- **Used By**: test_surgical_extraction_demo, aider_integration_service
- **Side Effects**: network_io, modifies_state
- **Potential Errors**: ZeroDivisionError, TypeError, IndexError, AttributeError, ImportError, KeyError

### 83. main (function)
- **Module**: test_aider_context_request
- **File**: test_aider_context_request.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: getcwd, MockCoder, process_context_requests, find, strip
- **Used By**: test_browser, test_ssl_verification, test_deprecated, test_main
- **Side Effects**: writes_log, network_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

### 84. tool_output (function)
- **Module**: test_aider_context_request
- **File**: test_aider_context_request.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: append
- **Used By**: base_coder, commands, gui, scrape, base_coder_old, onboarding, utils, test_context_request_hang, versioncheck, test_io
- **Side Effects**: writes_log, modifies_state
- **Potential Errors**: KeyError, TypeError, IndexError, ValueError, AttributeError

### 85. main (function)
- **Module**: test_context_request
- **File**: test_context_request.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: parse_args, getcwd, AiderIntegrationService, AiderContextRequestIntegration, get_llm_instructions, ContextRequest, SymbolRequest, get_context_request_summary, process_context_request, detect_context_request
- **Used By**: test_browser, test_ssl_verification, test_deprecated, test_main
- **Side Effects**: network_io, writes_log
- **Potential Errors**: ImportError, KeyError, TypeError, IndexError, ValueError, AttributeError

### 86. main (function)
- **Module**: test_context_request_availability
- **File**: test_context_request_availability.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: getcwd, AiderContextRequestIntegration, get_llm_instructions, ContextRequest, SymbolRequest, get_context_request_summary
- **Used By**: test_browser, test_ssl_verification, test_deprecated, test_main
- **Side Effects**: writes_log, network_io
- **Potential Errors**: KeyError, TypeError, IndexError, ValueError, AttributeError

### 87. main (function)
- **Module**: test_context_request_code_block_fix
- **File**: test_context_request_code_block_fix.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: test_context_request_code_block_closure, test_markdown_stream_handling, test_code_block_flow_simulation
- **Used By**: test_browser, test_ssl_verification, test_deprecated, test_main
- **Side Effects**: writes_log, network_io
- **Potential Errors**: KeyError, ZeroDivisionError, TypeError, IndexError, ValueError, AttributeError

### 88. main (function)
- **Module**: test_context_request_integration
- **File**: test_context_request_integration.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: MockCoder, time, process_context_requests, send, append
- **Used By**: test_browser, test_ssl_verification, test_deprecated, test_main
- **Side Effects**: writes_log, network_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, KeyError

### 89. main (function)
- **Module**: test_context_request_real
- **File**: test_context_request_real.py
- **Criticality**: medium
- **Change Risk**: medium
- **Relevance Score**: 2.4735897435897436
- **Priority**: critical
- **Calls**: getcwd, AiderContextRequestIntegration, ContextRequest, SymbolRequest, process_context_request, find, strip
- **Used By**: test_browser, test_ssl_verification, test_deprecated, test_main
- **Side Effects**: writes_log, network_io
- **Potential Errors**: TypeError, IndexError, ValueError, AttributeError, ImportError, KeyError

## SOURCE CODE IMPLEMENTATIONS (88 implementations)

### 1. parse_context_request
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.83974358974359

```python
    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try alternative pattern
                pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    return None

            # Get the matched content
            json_str = match.group(1).strip()

            # Clean up the JSON string
            # Remove any trailing }} that might be part of the CONTEXT_REQUEST format
            json_str = json_str.rstrip('}')

            # Ensure it's a valid JSON object
            if not json_str.startswith('{'):
                json_str = '{' + json_str
            if not json_str.endswith('}'):
                json_str = json_str + '}'

            # Replace any escaped quotes
            json_str = json_str.replace('\\"', '"')

            # Try to parse the JSON
            try:
                request_data = json.loads(json_str)
            except json.JSONDecodeError:
                # Try to fix common JSON formatting issues
                # Replace single quotes with double quotes
                json_str = json_str.replace("'", '"')
                # Fix unquoted keys
                json_str = re.sub(r'(\w+):', r'"\1":', json_str)
                request_data = json.loads(json_str)

            # Create the ContextRequest object
            symbols = []
            for symbol_data in request_data.get('symbols_of_interest', []):
                symbols.append(SymbolRequest(
                    type=symbol_data.get('type', 'unknown'),
                    name=symbol_data.get('name', ''),
                    file_hint=symbol_data.get('file_hint')
                ))

            return ContextRequest(
                original_user_query_context=request_data.get('original_user_query_context', ''),
                symbols_of_interest=symbols,
                reason_for_request=request_data.get('reason_for_request', '')
            )
        except Exception as e:
            print(f"Error parsing context request: {e}")
            return None

```

### 2. process_context_requests
- **File**: aider-main\aider\coders\base_coder.py
- **Priority**: critical
- **Relevance Score**: 2.7911538461538465

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
        if not hasattr(self, 'context_request_integration') or self.context_request_integration is None:
            try:
                from ..context_request import AiderContextRequestIntegration

                # Determine the correct project path for context requests
                # Priority: 1) Current working directory (where user runs aider)
                #          2) Git repository root (fallback)
                import os
                project_path = os.getcwd()

                # If current working directory is the aider repository itself,
                # and we have files in the chat, use the common root of those files
                if (os.path.basename(project_path) == 'aider' and
                    os.path.exists(os.path.join(project_path, 'aider-main')) and
                    (self.abs_fnames or self.abs_read_only_fnames)):
                    # Use the common root of the files in the chat
                    if self.abs_fnames:
                        project_path = utils.find_common_root(self.abs_fnames)
                    elif self.abs_read_only_fnames:
                        project_path = utils.find_common_root(self.abs_read_only_fnames)

                self.context_request_integration = AiderContextRequestIntegration(project_path, coder=self)
            except Exception as e:
                self.io.tool_error(f"Failed to initialize context request integration: {e}")
                return content, None

        # Check if we've reached the maximum number of context requests for this query
        if not hasattr(self, 'current_query_context_requests'):
            self.current_query_context_requests = 0

        # DISABLED: Context request limit check
        # if self.current_query_context_requests >= 300:
        #     self.io.tool_error(f"Maximum number of context requests reached for this query. Current count: {self.current_query_context_requests}")
        #     return content, None

        # Detect if there's a context request in the content
        try:
            context_request = self.context_request_integration.detect_context_request(content)
            if not context_request:
                return content, None
        except Exception as e:
            self.io.tool_error(f"Error detecting context request: {e}")
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        try:
            self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")
        except Exception as e:
            self.io.tool_output(f"Processing context request (error getting summary: {e})")

        # Get the repository overview
        repo_overview = ""
        if self.repo_map:
            try:
                # Try different methods to get the repository overview
                if hasattr(self.repo_map, 'get_repo_overview'):
                    repo_overview = self.repo_map.get_repo_overview()
                elif hasattr(self.repo_map, 'get_all_files') and callable(getattr(self.repo_map, 'get_all_files')):
                    try:
                        # Build a simple overview from the list of files
                        files = self.repo_map.get_all_files()
                        repo_overview = "\n".join(files)
                    except Exception as e:
                        self.io.tool_warning(f"Error calling get_all_files: {e}")
                elif hasattr(self.repo_map, 'files'):
                    # Build a simple overview from the files dictionary
                    repo_overview = "\n".join(self.repo_map.files.keys())
                else:
                    self.io.tool_warning("Repository map doesn't have a method to get an overview")
            except Exception as e:
                self.io.tool_warning(f"Error getting repository overview: {e}")

        # Clean up the content by removing the context request FIRST
        # This ensures the CONTEXT_REQUEST block is removed from display even if processing fails
        try:
            # Use a more robust pattern that handles both single and double closing braces
            context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}+\s*'
            cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)
        except Exception as e:
            self.io.tool_warning(f"Error cleaning content: {e}")
            cleaned_content = content

        # Process the context request and get the augmented prompt
        try:
            # CRITICAL FIX: Extract the ACTUAL user query from user_message
            # The user_message might contain MAP_REQUEST content, we need the original query
            actual_user_query = self._extract_actual_user_query(user_message)

            # CRITICAL FIX: Do NOT pass repo_overview if it contains MAP_REQUEST content
            # For CONTEXT_REQUEST, we want ONLY the clean template response
            clean_repo_overview = ""
            if repo_overview and not self._contains_map_request_content(repo_overview):
                clean_repo_overview = repo_overview

            # Only pass the existing conversation history, don't update it yet
            # We'll update it after we get the final response
            augmented_prompt = self.context_request_integration.process_context_request(
                context_request=context_request,
                original_user_query=actual_user_query,
                repo_overview=clean_repo_overview
            )

            # Check if the context request completely failed (no symbols found at all)
            # Only trigger failure guidance if ALL symbols were not found, not just some
            if (augmented_prompt and
                "All requested symbols could not be found" in augmented_prompt or
                ("No extracted symbols were included" in augmented_prompt and "could not find" in augmented_prompt.lower())):

                # Extract requested symbols for guidance
                requested_symbols = []
                try:
                    # context_request is a ContextRequest object, not a dict
                    symbols_of_interest = context_request.symbols_of_interest if hasattr(context_request, 'symbols_of_interest') else []
                    for symbol in symbols_of_interest:
                        if hasattr(symbol, 'name'):
                            requested_symbols.append(symbol.name)
                        elif isinstance(symbol, dict) and 'name' in symbol:
                            requested_symbols.append(symbol['name'])
                        elif isinstance(symbol, str):
                            requested_symbols.append(symbol)
                except Exception:
                    requested_symbols = ["requested symbols"]

                # Generate smart guidance message
                reason = ""
                if hasattr(context_request, 'reason_for_request'):
                    reason = context_request.reason_for_request
                elif hasattr(context_request, 'original_user_query_context'):
                    reason = context_request.original_user_query_context
                guidance_message = self._generate_smart_guidance_message("context", requested_symbols, reason)

                # Replace the augmented prompt with guidance
                augmented_prompt = guidance_message

        except Exception as e:
            self.io.tool_error(f"Error processing context request: {e}")
            # Return cleaned content even if processing fails, so CONTEXT_REQUEST block is removed
            return cleaned_content, None

        # Note: We're NOT updating conversation history here anymore
        # It will be updated after the final response is generated

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt

```

### 3. process_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.773076923076923

```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
        print(f"Repo overview length: {len(repo_overview)} characters")
        print(f"Conversation history: {self.conversation_history}")

        # Increment the iteration counter
        self.current_iteration += 1

        # Process the context request
        extracted_context = self.context_handler.process_context_request(context_request)

        # Log the extracted context
        print("\n=== EXTRACTED CONTEXT ===")
        print(f"Original user query context: {extracted_context.get('original_user_query_context', '')}")
        print(f"Reason for request: {extracted_context.get('reason_for_request', '')}")
        print(f"Number of extracted symbols: {len(extracted_context.get('extracted_symbols', []))}")
        print(f"Number of dependency snippets: {len(extracted_context.get('dependency_snippets', []))}")

        # Render the augmented prompt
        augmented_prompt = self.template_renderer.render_augmented_prompt(
            original_query=original_user_query,
            repo_overview=repo_overview,
            extracted_context=extracted_context,
            conversation_history=self.conversation_history
        )

        # Log the augmented prompt
        print("\n=== AUGMENTED PROMPT ===")
        print(augmented_prompt[:500] + "..." if len(augmented_prompt) > 500 else augmented_prompt)
        print("=== END OF CONTEXT REQUEST PROCESSING ===\n\n")

        return augmented_prompt

```

### 4. detect_context_request
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.748076923076923

```python
    def detect_context_request(self, llm_response: str) -> Optional[ContextRequest]:
        """
        Detect if the LLM response contains a context request.

        Args:
            llm_response: The LLM response to check

        Returns:
            A ContextRequest object if found, None otherwise
        """
        print("\n=== DETECTING CONTEXT REQUEST ===")
        print(f"LLM response (first 200 chars): {llm_response[:200]}..." if len(llm_response) > 200 else f"LLM response: {llm_response}")

        context_request = self.context_handler.parse_context_request(llm_response)

        if context_request:
            print(f"Context request detected: {context_request}")
            symbols = [s.name for s in context_request.symbols_of_interest]
            print(f"Symbols of interest: {', '.join(symbols)}")
        else:
            print("No context request detected")

        print("=== END OF CONTEXT REQUEST DETECTION ===\n")

        return context_request

```

### 5. process_context_request
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.723076923076923

```python
    def process_context_request(self, request: ContextRequest) -> Dict[str, Any]:
        """
        Process a context request, extracting the requested symbols and their dependencies.

        Args:
            request: The context request to process

        Returns:
            A dictionary containing the extracted context
        """
        # Create a cache key for this request
        cache_key = f"context_request:{','.join([s.name for s in request.symbols_of_interest])}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
            "dependency_snippets": []
        }

        # Process each requested symbol
        for symbol in request.symbols_of_interest:
            file_path, symbol_name, content = self._extract_symbol_content(symbol)
            if not file_path or not symbol_name or not content:
                continue

            # Extract essential imports
            essential_imports = None
            if hasattr(self.file_extractor, '_extract_essential_imports'):
                essential_imports = self.file_extractor._extract_essential_imports(self.project_path, file_path)

            # Extract containing class signature if it's a method
            containing_class = None
            if '.' in symbol.name and hasattr(self.file_extractor, '_extract_containing_class'):
                # Create a dummy SymbolInfo object
                from surgical_file_extractor import SymbolInfo
                symbol_info = SymbolInfo(
                    name=symbol_name,
                    start_line=0,  # This will be updated by the extractor
                    file_path=file_path,
                    symbol_type="method"
                )
                containing_class = self.file_extractor._extract_containing_class(self.project_path, file_path, symbol_info)

            # Extract usage contexts
            usage_contexts = self.context_extractor.extract_usage_contexts(self.project_path, symbol_name, file_path)

            # Add the extracted symbol to the result
            result["extracted_symbols"].append({
                "symbol_name": symbol.name,
                "file_path": file_path,
                "content": content,
                "essential_imports": essential_imports,
                "containing_class": containing_class
            })

            # Add dependency snippets
            for usage in usage_contexts[:3]:  # Limit to 3 usage examples
                result["dependency_snippets"].append({
                    "file_path": usage.snippet.file_path,
                    "symbol_name": usage.snippet.symbol_name,
                    "content": usage.snippet.content,
                    "usage_type": usage.usage_type.value
                })

        # Cache the result
        self._update_cache(cache_key, result)

        return result
```

### 6. update_conversation_history
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.706923076923077

```python
    def update_conversation_history(self, role: str, content: str) -> None:
        """
        Update the conversation history.

        Args:
            role: The role of the message (user or assistant)
            content: The content of the message
        """
        print("\n=== UPDATING CONVERSATION HISTORY ===")
        print(f"Adding message with role: {role}")
        print(f"Content (first 100 chars): {content[:100]}..." if len(content) > 100 else f"Content: {content}")
        print(f"Current history length: {len(self.conversation_history)}")

        # Log the current history before update
        if self.conversation_history:
            print("\nCurrent conversation history BEFORE update:")
            for i, msg in enumerate(self.conversation_history):
                print(f"Message {i+1} - Role: {msg.get('role', '')}")
                msg_content = msg.get('content', '')
                print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
                print("-" * 40)

        self.conversation_history.append({
            "role": role,
            "content": content
        })

        # Limit the conversation history to the last 10 messages
        # This prevents the history from growing too large and confusing the LLM
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
            print(f"Trimmed history to last 10 messages")

        # Log the updated history
        print("\nConversation history AFTER update:")
        for i, msg in enumerate(self.conversation_history):
            print(f"Message {i+1} - Role: {msg.get('role', '')}")
            msg_content = msg.get('content', '')
            print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
            print("-" * 40)

        print(f"New history length: {len(self.conversation_history)}")
        print("=== END OF CONVERSATION HISTORY UPDATE ===\n")

```

### 7. process_context_requests
- **File**: aider-main\aider\coders\base_coder_old.py
- **Priority**: critical
- **Relevance Score**: 2.6961538461538463

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
        if not hasattr(self, 'context_request_integration') or self.context_request_integration is None:
            try:
                from ..context_request import AiderContextRequestIntegration
                self.context_request_integration = AiderContextRequestIntegration(self.root, coder=self)
            except Exception as e:
                self.io.tool_error(f"Failed to initialize context request integration: {e}")
                return content, None

        # Check if we've reached the maximum number of context requests for this query
        if not hasattr(self, 'current_query_context_requests'):
            self.current_query_context_requests = 0

        if self.current_query_context_requests >= 300:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None

        # Detect if there's a context request in the content
        try:
            context_request = self.context_request_integration.detect_context_request(content)
            if not context_request:
                return content, None
        except Exception as e:
            self.io.tool_error(f"Error detecting context request: {e}")
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        try:
            self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")
        except Exception as e:
            self.io.tool_output(f"Processing context request (error getting summary: {e})")

        # Get the repository overview
        repo_overview = ""
        if self.repo_map:
            try:
                # Try different methods to get the repository overview
                if hasattr(self.repo_map, 'get_repo_overview'):
                    repo_overview = self.repo_map.get_repo_overview()
                elif hasattr(self.repo_map, 'get_all_files') and callable(getattr(self.repo_map, 'get_all_files')):
                    try:
                        # Build a simple overview from the list of files
                        files = self.repo_map.get_all_files()
                        repo_overview = "\n".join(files)
                    except Exception as e:
                        self.io.tool_warning(f"Error calling get_all_files: {e}")
                elif hasattr(self.repo_map, 'files'):
                    # Build a simple overview from the files dictionary
                    repo_overview = "\n".join(self.repo_map.files.keys())
                else:
                    self.io.tool_warning("Repository map doesn't have a method to get an overview")
            except Exception as e:
                self.io.tool_warning(f"Error getting repository overview: {e}")

        # Clean up the content by removing the context request FIRST
        # This ensures the CONTEXT_REQUEST block is removed from display even if processing fails
        try:
            # Use a more robust pattern that handles both single and double closing braces
            context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}+\s*'
            cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)
        except Exception as e:
            self.io.tool_warning(f"Error cleaning content: {e}")
            cleaned_content = content

        # Process the context request and get the augmented prompt
        try:
            # Only pass the existing conversation history, don't update it yet
            # We'll update it after we get the final response
            augmented_prompt = self.context_request_integration.process_context_request(
                context_request=context_request,
                original_user_query=user_message,
                repo_overview=repo_overview
            )
        except Exception as e:
            self.io.tool_error(f"Error processing context request: {e}")
            # Return cleaned content even if processing fails, so CONTEXT_REQUEST block is removed
            return cleaned_content, None

        # Note: We're NOT updating conversation history here anymore
        # It will be updated after the final response is generated

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt

```

### 8. _read_file_content
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.6535897435897438

```python
    def _read_file_content(self, project_path: str, file_path: str) -> Optional[str]:
        """Read the content of a file."""
        abs_path = os.path.join(project_path, file_path)
        return self.io.read_text(abs_path)

```

### 9. _extract_code_snippet
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.6235897435897435

```python
    def _extract_code_snippet(self, project_path: str, file_path: str,
                             line_num: int, context_window: int,
                             context_type: ContextType, symbol_name: str) -> Optional[CodeSnippet]:
        """
        Extract a code snippet from a file centered around a specific line.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to extract from
            line_num: The line number to center the snippet around (1-based)
            context_type: The type of context being extracted
            symbol_name: The name of the symbol being referenced

        Returns:
            A CodeSnippet object or None if extraction failed
        """
        content = self._read_file_content(project_path, file_path)
        if not content:
            return None

        lines = content.splitlines()
        if line_num < 1 or line_num > len(lines):
            return None

        # Adjust line_num to 0-based for internal calculations
        line_idx = line_num - 1

        # For class definitions, extract the complete class body
        if context_type == ContextType.DEFINITION and re.search(r'^\s*class\s+', lines[line_idx]):
            start_idx, end_idx = self._find_complete_class_body(lines, line_idx)
        # For function/method definitions, extract the complete function body
        elif context_type == ContextType.DEFINITION and re.search(r'^\s*def\s+', lines[line_idx]):
            start_idx, end_idx = self._find_complete_function_body(lines, line_idx)
        else:
            # Use context window for other cases
            start_idx = max(0, line_idx - context_window)
            end_idx = min(len(lines) - 1, line_idx + context_window)

        # Extract the surrounding function/method name if possible
        surrounding_function = self._find_surrounding_function(lines, line_idx)

        # Extract the snippet content
        snippet_lines = lines[start_idx:end_idx + 1]
        snippet_content = '\n'.join(snippet_lines)

        return CodeSnippet(
            content=snippet_content,
            file_path=file_path,
            start_line=start_idx + 1,  # Convert back to 1-based
            end_line=end_idx + 1,      # Convert back to 1-based
            context_type=context_type,
            symbol_name=symbol_name,
            surrounding_function=surrounding_function
        )

```

### 10. AiderContextRequestIntegration
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.61025641025641

```python
class AiderContextRequestIntegration:
    """
    Integrates the context request handler with the Aider system.
    """

```

### 11. SymbolRequest
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.61025641025641

```python
class SymbolRequest:
    """Represents a symbol requested by the LLM."""
    type: str  # method_definition, class_definition, function_definition, etc.
    name: str  # The name of the symbol, e.g., "AuthService.login_user"
    file_hint: Optional[str] = None  # Optional hint about which file contains the symbol


@dataclass
```

### 12. ContextRequest
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.61025641025641

```python
class ContextRequest:
    """Represents a context request from the LLM."""
    original_user_query_context: str
    symbols_of_interest: List[SymbolRequest]
    reason_for_request: str


```

### 13. ContextRequestHandler
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.61025641025641

```python
class ContextRequestHandler:
    """
    Handles context requests from the LLM, extracting the requested symbols
    and their dependencies using the surgical extraction system.
    """

```

### 14. select_optimal_context
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.605

```python
    def select_optimal_context(self, task_description: str, task_type: TaskType = TaskType.GENERAL_ANALYSIS,
                             focus_entities: Optional[List[str]] = None) -> ContextBundle:
        """
        Select the most relevant code context for a given task.

        Args:
            task_description: Natural language description of the task
            task_type: Type of development task (affects selection strategy)
            focus_entities: Optional list of specific entities to focus on

        Returns:
            ContextBundle containing the selected entities and metadata
        """
        print(f"🎯 Selecting optimal context for: {task_description}")
        print(f"   Task type: {task_type.value}")
        print(f"   Token budget: {self.max_tokens}")

        # Step 1: Score all entities for relevance
        scored_entities = self._score_entities_for_task(task_description, task_type, focus_entities)

        # Step 2: Select entities within token budget
        selected_entities = self._select_entities_within_budget(scored_entities, task_type)

        # Step 3: Enhance selection with dependency context
        enhanced_entities = self._enhance_with_dependency_context(selected_entities, task_type)

        # Step 4: Build the final context bundle
        context_bundle = self._build_context_bundle(
            task_description, task_type, enhanced_entities
        )

        print(f"✅ Context selection complete:")
        print(f"   Selected {len(context_bundle.entities)} entities")
        print(f"   Total tokens: {context_bundle.total_tokens}")
        print(f"   Critical entities: {len(context_bundle.get_critical_entities())}")

        return context_bundle

```

### 15. _get_context_selector
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5911538461538464

```python
    def _get_context_selector(self, project_path: str, max_tokens: int = 8000):
        """Get the Intelligent Context Selector, initializing it if necessary."""
        if self.context_selector is None:
            try:
                from intelligent_context_selector import IntelligentContextSelector

                # Generate IR data if not available
                ir_data = self.generate_mid_level_ir(project_path)

                # Create the context selector
                self.context_selector = IntelligentContextSelector(ir_data, max_tokens)
                print("✅ Intelligent Context Selector initialized")

            except ImportError as e:
                print(f"⚠️ Could not import IntelligentContextSelector: {e}")
                self.context_selector = None
            except Exception as e:
                print(f"⚠️ Error initializing context selector: {e}")
                self.context_selector = None

        return self.context_selector

```

### 16. test_all_possible_context_leaks
- **File**: test_hidden_context_detection.py
- **Priority**: critical
- **Relevance Score**: 2.58974358974359

```python
def test_all_possible_context_leaks():
    """Test ALL possible ways repository context could leak to the LLM."""
    print("🔍 Hidden Repository Context Detection")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        print("✅ Smart Map Request System is available")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        print("✅ Coder instance created successfully")
        
        # Test 1: Check ALL prompt attributes for embedded repository content
        print("\n🧪 Test 1: Checking ALL prompt attributes")
        print("-" * 60)
        
        prompt_attrs = [
            'main_system',
            'system_reminder', 
            'file_access_reminder',
            'repo_content_prefix',
            'reality_check_prompt',
            'files_content_prefix',
            'files_no_full_files_with_repo_map',
            'files_no_full_files_with_repo_map_reply',
            'files_content_gpt_edits',
            'files_content_gpt_no_edits',
        ]
        
        hidden_context_found = False
        
        for attr_name in prompt_attrs:
            if hasattr(coder.gpt_prompts, attr_name):
                attr_value = getattr(coder.gpt_prompts, attr_name)
                if attr_value:
                    content_length = len(str(attr_value))
                    print(f"   {attr_name}: {content_length} characters")
                    
                    # Check for repository content indicators
                    content_str = str(attr_value)
                    repo_indicators = ['⋮', 'class ', 'def ', 'function ', 'import ', 'from ']
                    repo_indicator_count = sum(1 for indicator in repo_indicators if indicator in content_str)
                    
                    if content_length > 5000 or repo_indicator_count > 20:
                        print(f"     ❌ SUSPICIOUS CONTENT! ({repo_indicator_count} indicators)")
                        print(f"     Preview: {content_str[:200]}...")
                        hidden_context_found = True
                    else:
                        print(f"     ✅ Clean ({repo_indicator_count} indicators)")
        
        # Test 2: Check if repository map is embedded in system prompts
        print("\n🧪 Test 2: Checking system prompt construction")
        print("-" * 60)
        
        try:
            # Try to get the formatted system prompt
            system_prompt = coder.fmt_system_prompt()
            if system_prompt:
                system_length = len(system_prompt)
                print(f"   System prompt length: {system_length} characters")
                
                # Check for repository content
                repo_indicators = ['⋮', 'class ', 'def ', 'function ', 'import ']
                repo_indicator_count = sum(1 for indicator in repo_indicators if indicator in system_prompt)
                
                if system_length > 10000 or repo_indicator_count > 50:
                    print(f"   ❌ REPOSITORY CONTENT IN SYSTEM PROMPT! ({repo_indicator_count} indicators)")
                    print(f"   Preview: {system_prompt[:300]}...")
                    hidden_context_found = True
                else:
                    print(f"   ✅ System prompt clean ({repo_indicator_count} indicators)")
            else:
                print("   ✅ System prompt is None/empty")
                
        except Exception as e:
            print(f"   ⚠️  Error getting system prompt: {e}")
        
        # Test 3: Check repository map generation and caching
        print("\n🧪 Test 3: Checking repository map generation")
        print("-" * 60)
        
        # Check if repository map is being generated
        repo_map_result = coder.get_repo_map()
        if repo_map_result:
            print(f"   ❌ REPOSITORY MAP IS BEING GENERATED: {len(repo_map_result)} characters")
            print(f"   Preview: {repo_map_result[:200]}...")
            hidden_context_found = True
        else:
            print("   ✅ get_repo_map() returns None")
        
        # Check if repository map is cached somewhere
        if hasattr(coder, 'repo_map') and coder.repo_map:
            print("   🔍 Checking RepoMap instance...")
            
            # Check if there's cached content
            if hasattr(coder.repo_map, 'cache') and coder.repo_map.cache:
                print(f"   ⚠️  RepoMap has cache: {len(coder.repo_map.cache)} items")
                for key, value in list(coder.repo_map.cache.items())[:3]:  # Check first 3 items
                    if value and len(str(value)) > 1000:
                        print(f"     ❌ LARGE CACHED CONTENT: {len(str(value))} characters")
                        hidden_context_found = True
            else:
                print("   ✅ No RepoMap cache")
        
        # Test 4: Check if context is embedded in model or IO
        print("\n🧪 Test 4: Checking model and IO for embedded context")
        print("-" * 60)
        
        # Check if model has embedded context
        if hasattr(model, 'context') or hasattr(model, 'system_prompt'):
            print("   ⚠️  Model has context/system_prompt attributes")
        else:
            print("   ✅ Model has no embedded context")
        
        # Check if IO has embedded context
        if hasattr(io, 'context') or hasattr(io, 'repo_content'):
            print("   ⚠️  IO has context/repo_content attributes")
        else:
            print("   ✅ IO has no embedded context")
        
        return not hidden_context_found
        
    except Exception as e:
        print(f"❌ Error in hidden context detection: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 17. get_context_request_summary
- **File**: aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.586410256410256

```python
    def get_context_request_summary(self, context_request: ContextRequest) -> str:
        """
        Get a summary of the context request for logging purposes.

        Args:
            context_request: The context request to summarize

        Returns:
            A summary of the context request
        """
        symbols = [s.name for s in context_request.symbols_of_interest]
        return f"Context request for symbols: {', '.join(symbols)}"
```

### 18. select_intelligent_context
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.582307692307692

```python
    def select_intelligent_context(self, project_path: str, task_description: str,
                                 task_type: str = "general_analysis",
                                 focus_entities: list = None, max_tokens: int = 8000):
        """
        Select the most relevant code context for a given task using AI-powered analysis.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_tokens: Maximum token budget for context selection

        Returns:
            Dictionary containing the selected context bundle with entities and metadata
        """
        try:
            # Get the context selector
            selector = self._get_context_selector(project_path, max_tokens)

            if selector is None:
                return {
                    'error': 'Context selector not available',
                    'fallback': 'Use traditional context extraction methods'
                }

            # Map string task type to enum
            from intelligent_context_selector import TaskType
            task_type_map = {
                'debugging': TaskType.DEBUGGING,
                'feature_development': TaskType.FEATURE_DEVELOPMENT,
                'code_review': TaskType.CODE_REVIEW,
                'refactoring': TaskType.REFACTORING,
                'documentation': TaskType.DOCUMENTATION,
                'testing': TaskType.TESTING,
                'general_analysis': TaskType.GENERAL_ANALYSIS
            }

            task_enum = task_type_map.get(task_type.lower(), TaskType.GENERAL_ANALYSIS)

            # Select optimal context
            context_bundle = selector.select_optimal_context(
                task_description=task_description,
                task_type=task_enum,
                focus_entities=focus_entities
            )

            # Analyze context quality
            quality_analysis = selector.analyze_context_quality(context_bundle)

            # Convert to dictionary format for easy consumption
            result = {
                'task_description': context_bundle.task_description,
                'task_type': context_bundle.task_type.value,
                'total_entities': len(context_bundle.entities),
                'total_tokens': context_bundle.total_tokens,
                'selection_rationale': context_bundle.selection_rationale,
                'quality_metrics': quality_analysis,
                'entities': []
            }

            # Add entity details
            for entity in context_bundle.entities:
                entity_info = {
                    'module_name': entity.module_name,
                    'entity_name': entity.entity_name,
                    'entity_type': entity.entity_type,
                    'file_path': entity.file_path,
                    'criticality': entity.criticality,
                    'change_risk': entity.change_risk,
                    'relevance_score': entity.relevance_score,
                    'priority': entity.priority.value,
                    'token_estimate': entity.token_estimate,
                    'dependency_depth': entity.dependency_depth,
                    'used_by': entity.used_by,
                    'calls': entity.calls,
                    'side_effects': entity.side_effects,
                    'errors': entity.errors
                }
                result['entities'].append(entity_info)

            # Sort entities by relevance score
            result['entities'].sort(key=lambda e: e['relevance_score'], reverse=True)

            return result

        except Exception as e:
            print(f"Error in intelligent context selection: {e}")
            return {
                'error': str(e),
                'fallback': 'Use traditional context extraction methods'
            }

```

### 19. analyze_context_quality
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.5799999999999996

```python
    def analyze_context_quality(self, context_bundle: ContextBundle) -> Dict[str, Any]:
        """Analyze the quality and completeness of a context bundle."""
        entities = context_bundle.entities

        # Calculate coverage metrics
        total_entities = len(self.entity_map)
        selected_entities = len(entities)
        coverage_percentage = (selected_entities / total_entities) * 100

        # Analyze priority distribution
        priority_dist = {}
        for priority in ContextPriority:
            count = len([e for e in entities if e.priority == priority])
            priority_dist[priority.value] = count

        # Analyze criticality distribution
        criticality_dist = {}
        for criticality in ['low', 'medium', 'high']:
            count = len([e for e in entities if e.criticality == criticality])
            criticality_dist[criticality] = count

        # Calculate dependency completeness
        missing_deps = 0
        total_deps = 0
        selected_names = {f"{e.module_name}.{e.entity_name}" for e in entities}

        for entity in entities:
            entity_name = f"{entity.module_name}.{entity.entity_name}"
            deps = self.dependency_graph.get(entity_name, set())
            total_deps += len(deps)
            missing_deps += len([d for d in deps if d not in selected_names])

        dependency_completeness = ((total_deps - missing_deps) / max(total_deps, 1)) * 100

        return {
            'coverage_percentage': coverage_percentage,
            'selected_entities': selected_entities,
            'total_entities': total_entities,
            'priority_distribution': priority_dist,
            'criticality_distribution': criticality_dist,
            'dependency_completeness': dependency_completeness,
            'token_utilization': (context_bundle.total_tokens / self.max_tokens) * 100,
            'average_relevance_score': sum(e.relevance_score for e in entities) / len(entities) if entities else 0
        }


```

### 20. cmd_copy_context
- **File**: aider-main\aider\commands.py
- **Priority**: critical
- **Relevance Score**: 2.566153846153846

```python
    def cmd_copy_context(self, args=None):
        """Copy the current chat context as markdown, suitable to paste into a web UI"""

        chunks = self.coder.format_chat_chunks()

        markdown = ""

        # Only include specified chunks in order
        for messages in [chunks.repo, chunks.readonly_files, chunks.chat_files]:
            for msg in messages:
                # Only include user messages
                if msg["role"] != "user":
                    continue

                content = msg["content"]

                # Handle image/multipart content
                if isinstance(content, list):
                    for part in content:
                        if part.get("type") == "text":
                            markdown += part["text"] + "\n\n"
                else:
                    markdown += content + "\n\n"

        args = args or ""
        markdown += f"""
Just tell me how to edit the files to make the changes.
Don't give me back entire files.
Just show me the edits I need to make.

{args}
"""

        try:
            pyperclip.copy(markdown)
            self.io.tool_output("Copied code context to clipboard.")
        except pyperclip.PyperclipException as e:
            self.io.tool_error(f"Failed to copy to clipboard: {str(e)}")
            self.io.tool_output(
                "You may need to install xclip or xsel on Linux, or pbcopy on macOS."
            )
        except Exception as e:
            self.io.tool_error(f"An unexpected error occurred while copying to clipboard: {str(e)}")


```

### 21. extract_usage_contexts
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.561923076923077

```python
    def extract_usage_contexts(self, project_path: str, symbol_name: str,
                              defining_file: str) -> List[UsageContext]:
        """
        Find all places where a specific symbol is used and extract the context around each usage.

        Args:
            project_path: Path to the project root
            symbol_name: Name of the symbol to find usages of
            defining_file: Path to the file that defines the symbol

        Returns:
            A list of UsageContext objects
        """
        cache_key = f"usage_contexts:{project_path}:{symbol_name}:{defining_file}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Find files that might use this symbol
        files_to_check = self.aider_service.get_files_that_import(project_path, defining_file)

        # Add the defining file itself (for self-references)
        if defining_file not in files_to_check:
            files_to_check.append(defining_file)

        usage_contexts = []

        # Check each file for usages of the symbol
        for file_path in files_to_check:
            # Find line numbers where the symbol is used
            line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol_name)

            for line_num in line_numbers:
                # Read the file content
                content = self._read_file_content(project_path, file_path)
                if not content:
                    continue

                # Determine the usage type
                usage_type = self._determine_usage_type(content, line_num, symbol_name)

                # Determine appropriate context window size
                adjusted_window = self._determine_context_window_size(
                    content, line_num - 1, "variable" if usage_type == UsageType.VARIABLE_REFERENCE else "function")

                # Extract the code snippet
                snippet = self._extract_code_snippet(
                    project_path, file_path, line_num, adjusted_window,
                    ContextType.USAGE, symbol_name)

                if snippet:
                    usage_context = UsageContext(
                        snippet=snippet,
                        usage_type=usage_type,
                        referenced_symbol=symbol_name,
                        defining_file=defining_file
                    )
                    usage_contexts.append(usage_context)

        # Cache the result
        self._update_cache(cache_key, usage_contexts)

        return usage_contexts

```

### 22. main
- **File**: test_real_context_request_fix.py
- **Priority**: critical
- **Relevance Score**: 2.5535897435897437

```python
def main():
    """Main test function."""
    success = test_real_context_request_fix()

    if success:
        print("\n🎉 REAL CONTEXT REQUEST FIX: PASSED")
        print("The fix correctly uses the project directory from command line arguments.")
    else:
        print("\n❌ REAL CONTEXT REQUEST FIX: FAILED")
        print("The fix needs further investigation.")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

### 23. test_intelligent_context_integration
- **File**: test_intelligent_context_selection.py
- **Priority**: critical
- **Relevance Score**: 2.5530769230769232

```python
def test_intelligent_context_integration():
    """Test the integration of Intelligent Context Selection with AiderIntegrationService."""
    print("🧪 Testing Intelligent Context Selection Integration")
    print("=" * 70)
    
    # Initialize the service
    service = AiderIntegrationService()
    project_path = os.getcwd()
    
    print(f"📁 Project path: {project_path}")
    
    # Define comprehensive test scenarios
    test_scenarios = [
        {
            'name': 'Bug Fix Scenario',
            'task': 'Fix a critical bug in the file parsing and processing logic that causes crashes',
            'type': 'debugging',
            'focus': ['file', 'parse', 'process', 'error'],
            'max_tokens': 3000
        },
        {
            'name': 'Feature Development Scenario',
            'task': 'Implement a new advanced code analysis feature with dependency tracking',
            'type': 'feature_development',
            'focus': ['analysis', 'dependency', 'tracking'],
            'max_tokens': 4000
        },
        {
            'name': 'Refactoring Scenario',
            'task': 'Refactor the entire dependency management and context extraction system',
            'type': 'refactoring',
            'focus': ['dependency', 'context', 'extraction'],
            'max_tokens': 3500
        },
        {
            'name': 'Code Review Scenario',
            'task': 'Review error handling and exception management throughout the codebase',
            'type': 'code_review',
            'focus': ['error', 'exception', 'handling'],
            'max_tokens': 2500
        },
        {
            'name': 'Testing Scenario',
            'task': 'Create comprehensive unit tests for the IR generation pipeline',
            'type': 'testing',
            'focus': ['test', 'unit', 'pipeline', 'ir'],
            'max_tokens': 2000
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🔍 Test {i}: {scenario['name']}")
        print(f"   Task: {scenario['task']}")
        print(f"   Type: {scenario['type']}")
        print(f"   Token Budget: {scenario['max_tokens']}")
        
        start_time = time.time()
        
        try:
            # Select intelligent context
            context_result = service.select_intelligent_context(
                project_path=project_path,
                task_description=scenario['task'],
                task_type=scenario['type'],
                focus_entities=scenario['focus'],
                max_tokens=scenario['max_tokens']
            )
            
            selection_time = time.time() - start_time
            
            if 'error' not in context_result:
                print(f"✅ Context selection successful in {selection_time:.2f}s")
                
                # Extract key metrics
                metrics = context_result['quality_metrics']
                
                print(f"📊 Results:")
                print(f"   Selected entities: {context_result['total_entities']}")
                print(f"   Token utilization: {metrics['token_utilization']:.1f}%")
                print(f"   Dependency completeness: {metrics['dependency_completeness']:.1f}%")
                print(f"   Average relevance score: {metrics['average_relevance_score']:.2f}")
                
                # Show priority distribution
                priority_dist = metrics['priority_distribution']
                print(f"   Priority distribution: {priority_dist}")
                
                # Show top 5 entities
                top_entities = context_result['entities'][:5]
                print(f"🏆 Top 5 Selected Entities:")
                for j, entity in enumerate(top_entities, 1):
                    print(f"     {j}. {entity['module_name']}.{entity['entity_name']}")
                    print(f"        Score: {entity['relevance_score']:.2f}, "
                          f"Priority: {entity['priority']}, "
                          f"Criticality: {entity['criticality']}")
                
                # Store results for analysis
                results.append({
                    'scenario': scenario['name'],
                    'success': True,
                    'selection_time': selection_time,
                    'metrics': metrics,
                    'entity_count': context_result['total_entities']
                })
                
            else:
                print(f"❌ Context selection failed: {context_result['error']}")
                results.append({
                    'scenario': scenario['name'],
                    'success': False,
                    'error': context_result['error']
                })
                
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'error': str(e)
            })
    
    # Print summary analysis
    print(f"\n📈 Summary Analysis")
    print("=" * 50)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ Successful tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed tests: {len(failed_tests)}")
    
    if successful_tests:
        avg_selection_time = sum(r['selection_time'] for r in successful_tests) / len(successful_tests)
        avg_entities = sum(r['entity_count'] for r in successful_tests) / len(successful_tests)
        avg_token_util = sum(r['metrics']['token_utilization'] for r in successful_tests) / len(successful_tests)
        avg_relevance = sum(r['metrics']['average_relevance_score'] for r in successful_tests) / len(successful_tests)
        
        print(f"\n📊 Performance Metrics:")
        print(f"   Average selection time: {avg_selection_time:.2f}s")
        print(f"   Average entities selected: {avg_entities:.1f}")
        print(f"   Average token utilization: {avg_token_util:.1f}%")
        print(f"   Average relevance score: {avg_relevance:.2f}")
    
    if failed_tests:
        print(f"\n⚠️ Failed Test Details:")
        for test in failed_tests:
            print(f"   - {test['scenario']}: {test['error']}")
    
    # Test specific entity lookup
    print(f"\n🔍 Testing Entity Lookup and Related Entities")
    try:
        # Get the context selector for direct testing
        selector = service._get_context_selector(project_path)
        
        if selector:
            # Test entity details lookup
            sample_entity = "aider_integration_service.AiderIntegrationService"
            entity_details = selector.get_entity_details(sample_entity)
            
            if entity_details:
                print(f"✅ Entity details for {sample_entity}:")
                print(f"   Criticality: {entity_details.criticality}")
                print(f"   Change risk: {entity_details.change_risk}")
                print(f"   Calls: {len(entity_details.calls)} functions")
                print(f"   Used by: {len(entity_details.used_by)} entities")
                
                # Get related entities
                related = selector.get_related_entities(sample_entity, max_depth=2)
                print(f"   Related entities (depth 2): {len(related)}")
                if related:
                    print(f"   Sample related: {related[:3]}")
            else:
                print(f"⚠️ Entity {sample_entity} not found")
        else:
            print("⚠️ Context selector not available for direct testing")
            
    except Exception as e:
        print(f"⚠️ Entity lookup test failed: {e}")
    
    print(f"\n🎉 Intelligent Context Selection Integration Test Complete!")
    
    return len(successful_tests) == len(results)


if __name__ == "__main__":
    success = test_intelligent_context_integration()
    exit(0 if success else 1)
```

### 24. test_context_request_clean_fix
- **File**: test_context_request_clean_fix.py
- **Priority**: critical
- **Relevance Score**: 2.548076923076923

```python
def test_context_request_clean_fix():
    """Test that CONTEXT_REQUEST responses are clean and don't contain MAP_REQUEST content."""

    print("🧪 Testing CONTEXT_REQUEST Clean Fix")
    print("=" * 50)

    try:
        print("✅ Testing helper functions directly")

        # Create a mock coder instance with just the methods we need
```

### 25. test_context_request_conversation_flow
- **File**: test_context_request_fix.py
- **Priority**: critical
- **Relevance Score**: 2.548076923076923

```python
def test_context_request_conversation_flow():
    """Test that context requests don't create recursive calls."""
    print("🧪 Testing Context Request Conversation Flow Fix")
    print("=" * 60)

    try:
        # Mock the necessary components
```

### 26. test_context_request_path_resolution
- **File**: test_context_request_path_fix.py
- **Priority**: critical
- **Relevance Score**: 2.548076923076923

```python
def test_context_request_path_resolution():
    """Test that context request system resolves to the correct project path."""
    
    print("🧪 Testing Context Request Path Resolution Fix")
    print("=" * 50)
    
    # Create a temporary trading project structure
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary project at: {temp_dir}")
        
        # Create the trading project structure
        trade_management_dir = os.path.join(temp_dir, "trade_management")
        services_dir = os.path.join(temp_dir, "services")
        os.makedirs(trade_management_dir)
        os.makedirs(services_dir)
        
        # Create position_exit_manager.py
        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""
    
    def __init__(self):
        self.active_positions = []
    
    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.
        
        Args:
            app: The application context containing market data and position info
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        # Check stop loss conditions
        if self._check_stop_loss(app):
            await self._execute_close(app, "stop_loss")
            return True
            
        # Check take profit conditions  
        if self._check_take_profit(app):
            await self._execute_close(app, "take_profit")
            return True
            
        return False
        
    def _check_stop_loss(self, app):
        """Check if stop loss conditions are met."""
        return False  # Placeholder
        
    def _check_take_profit(self, app):
        """Check if take profit conditions are met."""
        return False  # Placeholder
        
    async def _execute_close(self, app, reason):
        """Execute the position close."""
        print(f"Closing position due to: {reason}")
'''
        
        position_exit_manager_path = os.path.join(trade_management_dir, "position_exit_manager.py")
        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)
        
        # Create position_observer.py
        position_observer_content = '''
"""Position observation module."""

class PositionObserver:
    """Observes position changes and market conditions."""
    
    def __init__(self):
        self.observers = []
    
    def add_observer(self, observer):
        """Add a position observer."""
        self.observers.append(observer)
    
    def notify_position_change(self, position):
        """Notify all observers of position changes."""
        for observer in self.observers:
            observer.on_position_change(position)
    
    async def close_position_based_on_conditions(self, app):
        """
        Alternative implementation of position closing.
        
        Args:
            app: The application context
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        # This is a different implementation
        return self._evaluate_market_conditions(app)
        
    def _evaluate_market_conditions(self, app):
        """Evaluate market conditions for position closing."""
        return False  # Placeholder
'''
        
        position_observer_path = os.path.join(services_dir, "position_observer.py")
        with open(position_observer_path, 'w') as f:
            f.write(position_observer_content)
        
        print(f"✅ Created trading project structure:")
        print(f"   📄 {position_exit_manager_path}")
        print(f"   📄 {position_observer_path}")
        
        # Change to the temporary directory to simulate user running aider from project root
        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)
            print(f"📁 Changed working directory to: {temp_dir}")
            
            # Test the context request system
            try:
                # Import the context request system
                sys.path.insert(0, os.path.join(original_cwd, 'aider-main'))
                from aider.context_request.context_request_handler import ContextRequestHandler, SymbolRequest
                from aider.context_request.aider_integration_service import AiderIntegrationService
                
                print("✅ Successfully imported context request modules")
                
                # Create a context request handler with the current directory
                aider_service = AiderIntegrationService()
                handler = ContextRequestHandler(temp_dir, aider_service)
                
                print(f"📁 Handler project path: {handler.project_path}")
                
                # Test finding the files
                symbol1 = SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="trade_management/position_exit_manager.py"
                )
                
                symbol2 = SymbolRequest(
                    type="method_definition", 
                    name="close_position_based_on_conditions",
                    file_hint="services/position_observer.py"
                )
                
                # Test file resolution
                file1 = handler._find_file_for_symbol(symbol1)
                file2 = handler._find_file_for_symbol(symbol2)
                
                print(f"🔍 File 1 resolution: {file1}")
                print(f"🔍 File 2 resolution: {file2}")
                
                # Verify the files were found
                if file1 and file2:
                    print("✅ SUCCESS: Both files were found correctly!")
                    
                    # Test symbol extraction
                    file_path1, symbol_name1, content1 = handler._extract_symbol_content(symbol1)
                    file_path2, symbol_name2, content2 = handler._extract_symbol_content(symbol2)
                    
                    if content1 and content2:
                        print("✅ SUCCESS: Symbol content extracted successfully!")
                        print(f"   📄 Extracted from {file_path1}: {len(content1)} characters")
                        print(f"   📄 Extracted from {file_path2}: {len(content2)} characters")
                        return True
                    else:
                        print("❌ FAILED: Could not extract symbol content")
                        return False
                else:
                    print("❌ FAILED: Files were not found")
                    print(f"   Expected: trade_management/position_exit_manager.py")
                    print(f"   Expected: services/position_observer.py")
                    return False
                    
            except ImportError as e:
                print(f"❌ FAILED: Could not import context request modules: {e}")
                return False
            except Exception as e:
                print(f"❌ FAILED: Error during testing: {e}")
                import traceback
                traceback.print_exc()
                return False
                
        finally:
            os.chdir(original_cwd)
            print(f"📁 Restored working directory to: {original_cwd}")

```

### 27. test_context_request_root_fix
- **File**: test_context_request_root_fix.py
- **Priority**: critical
- **Relevance Score**: 2.548076923076923

```python
def test_context_request_root_fix():
    """Test that context request system uses the correct root path."""
    
    print("🧪 Testing Context Request Root Path Fix")
    print("=" * 50)
    
    # Create a temporary directory structure that mimics the real scenario
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary directory: {temp_dir}")
        
        # Create the aider directory structure
        aider_dir = os.path.join(temp_dir, "aider")
        aider_main_dir = os.path.join(aider_dir, "aider-main")
        os.makedirs(aider_main_dir)
        
        # Create a mock dashboard_repo_map.txt with external references
        dashboard_map_content = '''
# Repository Map

config\\base_config.py:
⋮
│BASE_SYMBOLS_CONFIG = {
│    'EURUSD': {
│        'mt5_symbol': 'EURUSD',
│        'price_key': 'EURUSD',
│        'symbol_allowed': True,
│    }
⋮

..\\trading_project\\services\\position_observer.py:
⋮
│class PositionObserver:
│    def close_position_based_on_conditions(self, app):
│        return True
⋮

..\\trading_project\\trade_management\\position_exit_manager.py:
⋮
│class PositionCloser:
│    def close_position_based_on_conditions(self, app):
│        return False
⋮
'''
        
        dashboard_map_path = os.path.join(aider_main_dir, "dashboard_repo_map.txt")
        with open(dashboard_map_path, 'w', encoding='utf-8') as f:
            f.write(dashboard_map_content)
        
        # Create the external trading project structure
        trading_dir = os.path.join(temp_dir, "trading_project")
        services_dir = os.path.join(trading_dir, "services")
        trade_mgmt_dir = os.path.join(trading_dir, "trade_management")
        os.makedirs(services_dir)
        os.makedirs(trade_mgmt_dir)
        
        # Create the actual trading files
        position_observer_content = '''
"""Position observation module."""

class PositionObserver:
    """Observes position changes and market conditions."""
    
    def __init__(self):
        self.observers = []
    
    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.
        
        Args:
            app: The application context
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        return self._evaluate_market_conditions(app)
        
    def _evaluate_market_conditions(self, app):
        """Evaluate market conditions for position closing."""
        return False  # Placeholder
'''
        
        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""
    
    def __init__(self):
        self.active_positions = []
    
    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.
        
        Args:
            app: The application context containing market data and position info
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        # Check stop loss conditions
        if self._check_stop_loss(app):
            await self._execute_close(app, "stop_loss")
            return True
            
        return False
        
    def _check_stop_loss(self, app):
        """Check if stop loss conditions are met."""
        return False  # Placeholder
        
    async def _execute_close(self, app, reason):
        """Execute the position close."""
        print(f"Closing position due to: {reason}")
'''
        
        position_observer_path = os.path.join(services_dir, "position_observer.py")
        position_exit_manager_path = os.path.join(trade_mgmt_dir, "position_exit_manager.py")
        
        with open(position_observer_path, 'w') as f:
            f.write(position_observer_content)
        
        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)
        
        print(f"✅ Created trading project structure:")
        print(f"   📄 {position_observer_path}")
        print(f"   📄 {position_exit_manager_path}")
        print(f"   📄 {dashboard_map_path}")
        
        # Change to the aider directory to simulate running aider from there
        original_cwd = os.getcwd()
        try:
            os.chdir(aider_dir)
            print(f"📁 Changed working directory to: {aider_dir}")
            
            # Test the context request root resolution
            try:
                # Import the required modules
                sys.path.insert(0, os.path.join(original_cwd, 'aider-main'))
                from aider.context_request.context_request_handler import ContextRequestHandler
                from aider.context_request.aider_integration_service import AiderIntegrationService
                
                print("✅ Successfully imported context request modules")
                
                # Test the root resolution logic
                # Simulate what the Coder class does
                root = aider_dir  # This would be self.root in the Coder class
                context_root = root
                
                # Apply the fix logic
                if (os.path.basename(root) == 'aider' and 
                    os.path.exists(os.path.join(root, 'aider-main'))):
                    # Check if there's a dashboard_repo_map.txt that indicates external project access
                    dashboard_map_path = os.path.join(root, 'aider-main', 'dashboard_repo_map.txt')
                    if os.path.exists(dashboard_map_path):
                        try:
                            with open(dashboard_map_path, 'r', encoding='utf-8') as f:
                                map_content = f.read()
                            # If the map contains references to external directories (..\ paths),
                            # use the parent directory as the context root
                            if '..\\' in map_content or '../' in map_content:
                                context_root = os.path.dirname(root)
                        except Exception:
                            pass  # Fall back to original root
                
                print(f"📁 Original root: {root}")
                print(f"📁 Context root after fix: {context_root}")
                
                # Verify the fix worked
                if context_root == temp_dir:
                    print("✅ SUCCESS: Context root correctly resolved to parent directory!")
                    
                    # Test that the context request handler can find the files
                    aider_service = AiderIntegrationService()
                    handler = ContextRequestHandler(context_root, aider_service)
                    
                    # Test file resolution
                    from aider.context_request.context_request_handler import SymbolRequest
                    
                    symbol1 = SymbolRequest(
                        type="method_definition",
                        name="close_position_based_on_conditions",
                        file_hint="trading_project/services/position_observer.py"
                    )
                    
                    symbol2 = SymbolRequest(
                        type="method_definition",
                        name="close_position_based_on_conditions", 
                        file_hint="trading_project/trade_management/position_exit_manager.py"
                    )
                    
                    # Test file resolution
                    file1 = handler._find_file_for_symbol(symbol1)
                    file2 = handler._find_file_for_symbol(symbol2)
                    
                    print(f"🔍 File 1 resolution: {file1}")
                    print(f"🔍 File 2 resolution: {file2}")
                    
                    if file1 and file2:
                        print("✅ SUCCESS: Both trading files found with the fixed context root!")
                        return True
                    else:
                        print("❌ FAILED: Files not found even with fixed context root")
                        return False
                else:
                    print(f"❌ FAILED: Context root not correctly resolved")
                    print(f"   Expected: {temp_dir}")
                    print(f"   Got: {context_root}")
                    return False
                    
            except ImportError as e:
                print(f"❌ FAILED: Could not import context request modules: {e}")
                return False
            except Exception as e:
                print(f"❌ FAILED: Error during testing: {e}")
                import traceback
                traceback.print_exc()
                return False
                
        finally:
            os.chdir(original_cwd)
            print(f"📁 Restored working directory to: {original_cwd}")

```

### 28. test_real_context_request_fix
- **File**: test_real_context_request_fix.py
- **Priority**: critical
- **Relevance Score**: 2.548076923076923

```python
def test_real_context_request_fix():
    """Test that context request system uses the project directory from command line."""

    print("🧪 Testing Real Context Request Fix")
    print("=" * 50)

    # Create a temporary directory structure that mimics the real scenario
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary directory: {temp_dir}")

        # Create the trading project structure
        trading_dir = os.path.join(temp_dir, "live_backtest_dashboard")
        services_dir = os.path.join(trading_dir, "services")
        trade_mgmt_dir = os.path.join(trading_dir, "trade_management")
        os.makedirs(services_dir)
        os.makedirs(trade_mgmt_dir)

        # Initialize a git repository in the trading project
        import subprocess
        subprocess.run(["git", "init"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=trading_dir, capture_output=True)

        # Create the actual trading files
        position_observer_content = '''
"""Position observation module."""

class PositionObserver:
    """Observes position changes and market conditions."""

    def __init__(self):
        self.observers = []

    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.

        Args:
            app: The application context

        Returns:
            bool: True if position was closed, False otherwise
        """
        return self._evaluate_market_conditions(app)

    def _evaluate_market_conditions(self, app):
        """Evaluate market conditions for position closing."""
        return False  # Placeholder
'''

        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""

    def __init__(self):
        self.active_positions = []

    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.

        Args:
            app: The application context containing market data and position info

        Returns:
            bool: True if position was closed, False otherwise
        """
        # Check stop loss conditions
        if self._check_stop_loss(app):
            await self._execute_close(app, "stop_loss")
            return True

        return False

    def _check_stop_loss(self, app):
        """Check if stop loss conditions are met."""
        return False  # Placeholder

    async def _execute_close(self, app, reason):
        """Execute the position close."""
        print(f"Closing position due to: {reason}")
'''

        position_observer_path = os.path.join(services_dir, "position_observer.py")
        position_exit_manager_path = os.path.join(trade_mgmt_dir, "position_exit_manager.py")

        with open(position_observer_path, 'w') as f:
            f.write(position_observer_content)

        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)

        # Add files to git
        subprocess.run(["git", "add", "."], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=trading_dir, capture_output=True)

        print(f"✅ Created trading project structure:")
        print(f"   📄 {position_observer_path}")
        print(f"   📄 {position_exit_manager_path}")
        print(f"   📁 Git repository initialized in: {trading_dir}")

        # Test the GitRepo and Coder initialization
        try:
            # Import the required modules
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
            from aider.repo import GitRepo
            from aider.coders.base_coder import Coder
            from aider.models import Model
            from aider.io import InputOutput

            print("✅ Successfully imported aider modules")

            # Create a GitRepo with the trading directory as git_dname
            io = InputOutput()
            repo = GitRepo(
                io=io,
                fnames=[],
                git_dname=trading_dir,  # This simulates the command line argument
                aider_ignore_file=None
            )

            print(f"📁 GitRepo root: {repo.root}")
            print(f"📁 GitRepo original git_dname: {getattr(repo, '_original_git_dname', 'NOT SET')}")

            # Verify that the original git_dname is stored
            if hasattr(repo, '_original_git_dname') and repo._original_git_dname == trading_dir:
                print("✅ SUCCESS: GitRepo correctly stores original git_dname")
            else:
                print("❌ FAILED: GitRepo does not store original git_dname correctly")
                return False

            # Create a Coder instance
            model = Model("gpt-3.5-turbo")
            coder = Coder.create(
                main_model=model,
                edit_format="informative",  # Use the correct edit format
                io=io,
                repo=repo,
                fnames=[],
                use_git=True,
                map_tokens=1000
            )

            print(f"📁 Coder root: {coder.root}")

            # Check if context request integration is initialized correctly
            if hasattr(coder, 'context_request_integration'):
                context_root = getattr(coder.context_request_integration, 'project_path', None)
                print(f"📁 Context request root: {context_root}")

                # The context request system should use the original trading directory
                if context_root == trading_dir:
                    print("✅ SUCCESS: Context request system uses the correct project directory!")

                    # Test that the context request can find the trading files
                    from aider.context_request.context_request_handler import SymbolRequest

                    symbol1 = SymbolRequest(
                        type="method_definition",
                        name="close_position_based_on_conditions",
                        file_hint="services/position_observer.py"
                    )

                    symbol2 = SymbolRequest(
                        type="method_definition",
                        name="close_position_based_on_conditions",
                        file_hint="trade_management/position_exit_manager.py"
                    )

                    # Test file resolution
                    handler = coder.context_request_integration.handler
                    file1 = handler._find_file_for_symbol(symbol1)
                    file2 = handler._find_file_for_symbol(symbol2)

                    print(f"🔍 File 1 resolution: {file1}")
                    print(f"🔍 File 2 resolution: {file2}")

                    if file1 and file2:
                        print("✅ SUCCESS: Both trading files found with the fixed context request system!")
                        return True
                    else:
                        print("❌ FAILED: Files not found even with fixed context request system")
                        return False
                else:
                    print(f"❌ FAILED: Context request system uses wrong directory")
                    print(f"   Expected: {trading_dir}")
                    print(f"   Got: {context_root}")
                    return False
            else:
                print("❌ FAILED: Context request integration not initialized")
                return False

        except ImportError as e:
            print(f"❌ FAILED: Could not import aider modules: {e}")
            return False
        except Exception as e:
            print(f"❌ FAILED: Error during testing: {e}")
            import traceback
            traceback.print_exc()
            return False

```

### 29. integrate_prioritization_with_algorithm
- **File**: fix_map_slicing.py
- **Priority**: critical
- **Relevance Score**: 2.541153846153846

```python
def integrate_prioritization_with_algorithm():
    """Integrate the prioritization with the ranking algorithm"""
    
    print("\n🔗 Integrating Prioritization with Ranking Algorithm")
    print("=" * 55)
    
    repomap_path = Path("aider-main/aider/repomap.py")
    
    # Read the current content
    with open(repomap_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find where ranked_tags is used in the binary search
    old_usage = "ranked_tags = self.get_ranked_tags("
    new_usage = """ranked_tags = self.get_ranked_tags(
            chat_fnames,
            other_fnames,
            mentioned_fnames,
            mentioned_idents,
            progress=spin.step,
        )
        
        # Apply intelligent prioritization for better file selection
        ranked_tags = apply_intelligent_ranking(ranked_tags, max_map_tokens)"""
    
    # Replace the usage
    if old_usage in content:
        # Find the complete function call
        start_idx = content.find(old_usage)
        if start_idx != -1:
            # Find the end of the function call
            paren_count = 0
            end_idx = start_idx
            for i, char in enumerate(content[start_idx:]):
                if char == '(':
                    paren_count += 1
                elif char == ')':
                    paren_count -= 1
                    if paren_count == 0:
                        end_idx = start_idx + i + 1
                        break
            
            # Replace the function call
            old_call = content[start_idx:end_idx]
            updated_content = content.replace(old_call, new_usage)
            
            # Write the updated content
            with open(repomap_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            print("✅ Integrated intelligent prioritization with ranking algorithm")
            return True
    
    print("❌ Could not find ranked_tags usage to modify")
    return False


```

### 30. test_aider_coder_context_request_path
- **File**: test_aider_coder_path_fix.py
- **Priority**: critical
- **Relevance Score**: 2.541153846153846

```python
def test_aider_coder_context_request_path():
    """Test that Aider Coder uses the correct project path for context requests."""
    
    print("🧪 Testing Aider Coder Context Request Path Fix")
    print("=" * 50)
    
    # Create a temporary trading project structure
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary project at: {temp_dir}")
        
        # Create the trading project structure
        trade_management_dir = os.path.join(temp_dir, "trade_management")
        services_dir = os.path.join(temp_dir, "services")
        os.makedirs(trade_management_dir)
        os.makedirs(services_dir)
        
        # Create position_exit_manager.py
        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""
    
    async def close_position_based_on_conditions(self, app):
        """Close positions based on predefined conditions."""
        return True
'''
        
        position_exit_manager_path = os.path.join(trade_management_dir, "position_exit_manager.py")
        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)
        
        print(f"✅ Created: {position_exit_manager_path}")
        
        # Change to the temporary directory to simulate user running aider from project root
        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)
            print(f"📁 Changed working directory to: {temp_dir}")
            
            # Test the Aider Coder context request path resolution
            try:
                # Import the required modules
                sys.path.insert(0, os.path.join(original_cwd, 'aider-main'))
                from aider.coders.base_coder import Coder
                from aider.models import Model
                from aider.io import InputOutput
                
                print("✅ Successfully imported Aider modules")
                
                # Create a mock Coder instance
                model = Model("gpt-3.5-turbo")
                io = InputOutput()
                
                # Create a minimal coder instance with a file from the project
                coder = Coder.create(
                    main_model=model,
                    io=io,
                    fnames=[position_exit_manager_path],  # Add the file to the chat
                    use_git=False,
                    map_tokens=1000
                )
                
                print(f"📁 Coder root: {coder.root}")
                print(f"📁 Current working directory: {os.getcwd()}")
                
                # Simulate a context request
                context_request_content = '''
{CONTEXT_REQUEST: {"original_user_query_context": "how does the close_position_based_on_conditions function work?", "symbols_of_interest": [{"type": "method_definition", "name": "close_position_based_on_conditions", "directory_name": "trade_management", "file_name": "position_exit_manager.py"}]}}
'''
                
                # Process the context request
                cleaned_content, augmented_prompt = coder.process_context_requests(
                    context_request_content, 
                    "how does the close_position_based_on_conditions function work?"
                )
                
                print(f"🔍 Cleaned content: {cleaned_content.strip()}")
                print(f"🔍 Augmented prompt length: {len(augmented_prompt) if augmented_prompt else 0}")
                
                # Check if the context request was processed successfully
                if augmented_prompt and "could not find" not in augmented_prompt.lower():
                    print("✅ SUCCESS: Context request processed successfully!")
                    print("✅ The fix correctly resolves project paths in Aider Coder!")
                    return True
                else:
                    print("❌ FAILED: Context request failed to find symbols")
                    if augmented_prompt:
                        print(f"   Augmented prompt: {augmented_prompt[:200]}...")
                    return False
                    
            except ImportError as e:
                print(f"❌ FAILED: Could not import Aider modules: {e}")
                return False
            except Exception as e:
                print(f"❌ FAILED: Error during testing: {e}")
                import traceback
                traceback.print_exc()
                return False
                
        finally:
            os.chdir(original_cwd)
            print(f"📁 Restored working directory to: {original_cwd}")

```

### 31. test_partial_success_context_request
- **File**: test_partial_success_fix.py
- **Priority**: critical
- **Relevance Score**: 2.541153846153846

```python
def test_partial_success_context_request():
    """Test that context request system provides partial success when some symbols are found."""
    
    print("🧪 Testing Partial Success Context Request Fix")
    print("=" * 60)
    
    # Create a temporary directory structure that mimics the real scenario
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temporary directory: {temp_dir}")
        
        # Create the trading project structure
        trading_dir = os.path.join(temp_dir, "live_backtest_dashboard")
        services_dir = os.path.join(trading_dir, "services")
        trade_mgmt_dir = os.path.join(trading_dir, "trade_management")
        os.makedirs(services_dir)
        os.makedirs(trade_mgmt_dir)
        
        # Initialize a git repository in the trading project
        import subprocess
        subprocess.run(["git", "init"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=trading_dir, capture_output=True)
        
        # Create the actual trading files
        # File 1: position_observer.py - WITHOUT close_position_based_on_conditions
        position_observer_content = '''
"""Position observation module."""

class PositionObserver:
    """Observes position changes and market conditions."""
    
    def __init__(self):
        self.observers = []
    
    def observe_position_open(self, app):
        """Observe when a position is opened."""
        return True
        
    def observe_position_close(self, app):
        """Observe when a position is closed."""
        return True
        
    def _evaluate_market_conditions(self, app):
        """Evaluate market conditions for position closing."""
        return False  # Placeholder
'''
        
        # File 2: position_exit_manager.py - WITH close_position_based_on_conditions
        position_exit_manager_content = '''
"""Position exit management module."""

class PositionCloser:
    """Handles position closing logic."""
    
    def __init__(self):
        self.active_positions = []
    
    async def close_position_based_on_conditions(self, app):
        """
        Close positions based on predefined conditions.
        
        Args:
            app: The application context containing market data and position info
            
        Returns:
            bool: True if position was closed, False otherwise
        """
        # Check stop loss conditions
        if self._check_stop_loss(app):
            await self._execute_close(app, "stop_loss")
            return True
            
        return False
        
    def _check_stop_loss(self, app):
        """Check if stop loss conditions are met."""
        return False  # Placeholder
        
    async def _execute_close(self, app, reason):
        """Execute the position close."""
        print(f"Closing position due to: {reason}")
'''
        
        position_observer_path = os.path.join(services_dir, "position_observer.py")
        position_exit_manager_path = os.path.join(trade_mgmt_dir, "position_exit_manager.py")
        
        with open(position_observer_path, 'w') as f:
            f.write(position_observer_content)
        
        with open(position_exit_manager_path, 'w') as f:
            f.write(position_exit_manager_content)
        
        # Add files to git
        subprocess.run(["git", "add", "."], cwd=trading_dir, capture_output=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=trading_dir, capture_output=True)
        
        print(f"✅ Created trading project structure:")
        print(f"   📄 {position_observer_path} (WITHOUT close_position_based_on_conditions)")
        print(f"   📄 {position_exit_manager_path} (WITH close_position_based_on_conditions)")
        
        # Test the context request system
        try:
            # Import the required modules
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))
            from aider.context_request.context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
            from aider.context_request.aider_context_request_integration import AiderContextRequestIntegration
            
            print("✅ Successfully imported context request modules")
            
            # Create the context request integration
            integration = AiderContextRequestIntegration(trading_dir)
            
            # Create a context request that asks for the function in BOTH files
            # (simulating the real scenario where LLM incorrectly assumes it exists in both)
            symbol1 = SymbolRequest(
                type="method_definition",
                name="close_position_based_on_conditions",
                file_hint="services/position_observer.py"  # This will NOT be found
            )
            
            symbol2 = SymbolRequest(
                type="method_definition", 
                name="close_position_based_on_conditions",
                file_hint="trade_management/position_exit_manager.py"  # This WILL be found
            )
            
            context_request = ContextRequest(
                original_user_query_context="how does the close_position_based_on_conditions function work?",
                symbols_of_interest=[symbol1, symbol2],
                reason_for_request="User wants to understand how position closing works"
            )
            
            print("🔍 Testing context request with partial success scenario:")
            print(f"   ❌ Requesting from: services/position_observer.py (should NOT be found)")
            print(f"   ✅ Requesting from: trade_management/position_exit_manager.py (should be found)")
            
            # Process the context request
            augmented_prompt = integration.process_context_request(
                context_request=context_request,
                original_user_query="how does the close_position_based_on_conditions function work?",
                repo_overview="Trading system repository"
            )
            
            print(f"\n📝 Augmented prompt length: {len(augmented_prompt)} characters")
            
            # Check if the response contains partial success indicators
            success_indicators = [
                "### EXTRACTED SYMBOLS",  # Should contain found symbols
                "### ⚠️ SYMBOLS NOT FOUND ⚠️",  # Should contain not found symbols
                "close_position_based_on_conditions",  # Should contain the function
                "position_exit_manager.py"  # Should reference the file where it was found
            ]
            
            failure_indicators = [
                "❌ **Context Request Failed**",  # Should NOT show complete failure
                "All requested symbols could not be found"  # Should NOT show complete failure
            ]
            
            found_success = []
            found_failure = []
            
            for indicator in success_indicators:
                if indicator in augmented_prompt:
                    found_success.append(indicator)
                    
            for indicator in failure_indicators:
                if indicator in augmented_prompt:
                    found_failure.append(indicator)
            
            print(f"\n✅ Success indicators found: {len(found_success)}/{len(success_indicators)}")
            for indicator in found_success:
                print(f"   ✅ Found: {indicator}")
                
            print(f"\n❌ Failure indicators found: {len(found_failure)}/{len(failure_indicators)}")
            for indicator in found_failure:
                print(f"   ❌ Found: {indicator}")
            
            # Test result
            if len(found_success) >= 3 and len(found_failure) == 0:
                print("\n🎉 SUCCESS: Partial success working correctly!")
                print("   ✅ System provides found symbols")
                print("   ✅ System notes missing symbols") 
                print("   ✅ System does NOT show complete failure")
                return True
            else:
                print("\n❌ FAILED: Partial success not working correctly")
                if len(found_failure) > 0:
                    print("   ❌ System still shows complete failure")
                if len(found_success) < 3:
                    print("   ❌ System doesn't provide adequate partial success information")
                
                # Show a snippet of the response for debugging
                print(f"\n🔍 Response snippet (first 500 chars):")
                print(augmented_prompt[:500])
                print("...")
                return False
                
        except ImportError as e:
            print(f"❌ FAILED: Could not import context request modules: {e}")
            return False
        except Exception as e:
            print(f"❌ FAILED: Error during testing: {e}")
            import traceback
            traceback.print_exc()
            return False

```

### 32. extract_dependency_contexts
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.5319230769230767

```python
    def extract_dependency_contexts(self, project_path: str, primary_file: str,
                                  context_window: int = 10) -> Dict[str, List[CodeSnippet]]:
        """
        Extract focused code snippets from all dependencies of a primary file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            context_window: Default number of lines to include above and below each reference

        Returns:
            Dictionary mapping file paths to lists of code snippets
        """
        cache_key = f"dependency_contexts:{project_path}:{primary_file}:{context_window}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Get files imported by the primary file
        imported_files = self.aider_service.get_files_imported_by(project_path, primary_file)

        # Get files that import the primary file
        importing_files = self.aider_service.get_files_that_import(project_path, primary_file)

        # Combine all related files
        related_files = list(set(imported_files + importing_files))

        # Initialize result dictionary
        result = {file_path: [] for file_path in related_files + [primary_file]}

        # Process each related file
        for file_path in related_files:
            # Get symbol references between the files
            symbol_refs = self.aider_service.get_symbol_references_between_files(
                project_path, primary_file, file_path)

            # Extract snippets for each referenced symbol
            for symbol_type, symbols in symbol_refs.items():
                for symbol in symbols:
                    # Find line numbers where the symbol is defined in the target file
                    line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol)

                    for line_num in line_numbers:
                        # Determine appropriate context window size
                        content = self._read_file_content(project_path, file_path)
                        if content:
                            adjusted_window = self._determine_context_window_size(
                                content, line_num - 1, symbol_type)  # Convert to 0-based
                        else:
                            adjusted_window = context_window

                        # Extract the code snippet
                        snippet = self._extract_code_snippet(
                            project_path, file_path, line_num, adjusted_window,
                            ContextType.DEFINITION, symbol)

                        if snippet:
                            result[file_path].append(snippet)

            # Also check references from the related file to the primary file
            reverse_refs = self.aider_service.get_symbol_references_between_files(
                project_path, file_path, primary_file)

            # Extract snippets for each referenced symbol
            for symbol_type, symbols in reverse_refs.items():
                for symbol in symbols:
                    # Find line numbers where the symbol is used in the source file
                    line_numbers = self._find_symbol_line_numbers(project_path, file_path, symbol)

                    for line_num in line_numbers:
                        # Determine appropriate context window size
                        content = self._read_file_content(project_path, file_path)
                        if content:
                            adjusted_window = self._determine_context_window_size(
                                content, line_num - 1, symbol_type)  # Convert to 0-based
                        else:
                            adjusted_window = context_window

                        # Extract the code snippet
                        snippet = self._extract_code_snippet(
                            project_path, file_path, line_num, adjusted_window,
                            ContextType.USAGE, symbol)

                        if snippet:
                            result[file_path].append(snippet)

        # Cache the result
        self._update_cache(cache_key, result)

        return result

```

### 33. test_aider_integration
- **File**: test_aider_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.5319230769230767

```python
def test_aider_integration(args):
    """Test integration with Aider."""
    print("\n=== Testing Aider Integration ===")
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the integration service
    aider_service = AiderIntegrationService()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path, aider_service)
    
    # Print the LLM instructions
    print("\nLLM Instructions:")
    instructions = integration.get_llm_instructions()
    print(instructions)
    
    # Check if the instructions contain the expected content
    if "CONTEXT_REQUEST" not in instructions:
        print("Error: LLM instructions do not contain CONTEXT_REQUEST")
        return False
    
    # Create a sample context request
    context_request = ContextRequest(
        original_user_query_context="User is asking about the surgical file extractor",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name=args.symbol,
                file_hint=args.file_hint
            )
        ],
        reason_for_request=args.reason
    )
    
    # Process the context request
    print("\nProcessing context request:")
    print(f"Request: {integration.get_context_request_summary(context_request)}")
    
    # Sample repository overview
    repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
surgical_context_extractor.py:
│class SurgicalContextExtractor:
│    def extract_usage_contexts(self, project_path, symbol_name, defining_file):
│    def extract_dependency_contexts(self, project_path, primary_file):
│    def extract_definition_contexts(self, project_path, symbols, source_file):
"""
    
    # Generate the augmented prompt
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the surgical file extractor work?",
        repo_overview=repo_overview
    )
    
    # Check if the augmented prompt contains the expected content
    if "REPOSITORY OVERVIEW" not in augmented_prompt:
        print("Error: Augmented prompt does not contain repository overview")
        return False
    
    if "INSTRUCTIONS FOR THIS TURN" not in augmented_prompt:
        print("Error: Augmented prompt does not contain instructions")
        return False
    
    # Test conversation history update
    integration.update_conversation_history("user", "How does the surgical file extractor work?")
    integration.update_conversation_history("assistant", "I'll explain how it works.")
    
    # Check if the conversation history was updated
    if len(integration.conversation_history) != 2:
        print("Error: Conversation history was not updated correctly")
        return False
    
    # Test iteration counter
    integration.reset_iteration_counter()
    if integration.current_iteration != 0:
        print("Error: Iteration counter was not reset correctly")
        return False
    
    print("\nAider integration test passed!")
    return True


```

### 34. test_markdown_stream_handling
- **File**: test_context_request_code_block_fix.py
- **Priority**: critical
- **Relevance Score**: 2.5319230769230767

```python
def test_markdown_stream_handling():
    """
    Test that the markdown stream handling code is present in the CONTEXT_REQUEST processing.
    """
    
    try:
        print("\n=== MARKDOWN STREAM HANDLING TEST ===")
        
        # Read the base_coder.py file to check for the fix
        file_path = "aider-main/aider/coders/base_coder.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the markdown stream closure code
        if "if self.mdstream:" in content and "self.live_incremental_response(True)" in content:
            print("✅ Markdown stream closure code found")
        else:
            print("❌ Markdown stream closure code not found")
            return False
        
        # Check for markdown stream reinitialization code
        if "self.mdstream = self.io.get_assistant_mdstream()" in content:
            print("✅ Markdown stream reinitialization code found")
        else:
            print("❌ Markdown stream reinitialization code not found")
            return False
        
        # Check that the fix is in the right place (context request processing)
        context_request_section = content[content.find("process_context_requests"):content.find("process_context_requests") + 2000]
        
        if "self.live_incremental_response(True)" in context_request_section:
            print("✅ Markdown stream fix is in the correct location (context request processing)")
        else:
            print("❌ Markdown stream fix not found in context request processing section")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

```

### 35. _update_cache
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.5285897435897438

```python
    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value and timestamp."""
        self.context_cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

```

### 36. process_context_requests
- **File**: test_aider_context_request.py
- **Priority**: critical
- **Relevance Score**: 2.528076923076923

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        # Initialize the context request integration if not already done
        if not hasattr(self, 'context_request_integration') or self.context_request_integration is None:
            from aider.context_request import AiderContextRequestIntegration
            self.context_request_integration = AiderContextRequestIntegration(self.root, coder=self)

        # Check if we've reached the maximum number of context requests for this query
        if self.current_query_context_requests >= 3:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None

        # Detect if there's a context request in the content
        context_request = self.context_request_integration.detect_context_request(content)
        if not context_request:
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Process the context request
        try:
            augmented_prompt = self.context_request_integration.process_context_request(
                context_request=context_request,
                original_user_query=user_message,
                repo_overview="This is a mock repository overview."
            )
        except Exception as e:
            self.io.tool_error(f"Error processing context request: {e}")
            return content, None

        # Clean up the content by removing the context request
        import re
        context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
        cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)

        return cleaned_content, augmented_prompt

```

### 37. process_context_requests
- **File**: test_context_request_hang.py
- **Priority**: critical
- **Relevance Score**: 2.528076923076923

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.
        
        Args:
            content: The LLM response content
            user_message: The original user message
            
        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        
        # Check if context request integration is available
        if not self.context_request_integration:
            self.io.tool_error("Context request integration not available")
            return content, None
        
        # Check if we've reached the maximum number of context requests for this query
        if self.current_query_context_requests >= 3:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None
        
        # Detect if there's a context request in the content
        context_request = self.context_request_integration.detect_context_request(content)
        if not context_request:
            return content, None
        
        # Increment the context request counter
        self.current_query_context_requests += 1
        
        # Log the context request
        self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")
        
        # Get a sample repository overview
        repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
"""
        
        # Process the context request and get the augmented prompt
        augmented_prompt = self.context_request_integration.process_context_request(
            context_request=context_request,
            original_user_query=user_message,
            repo_overview=repo_overview
        )
        
        # Update the conversation history in the context request integration
        self.context_request_integration.update_conversation_history("user", user_message)
        self.context_request_integration.update_conversation_history("assistant", content)
        
        # Clean up the content by removing the context request
        context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
        cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)
        
        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt
    
```

### 38. tool_output
- **File**: test_context_request_hang.py
- **Priority**: critical
- **Relevance Score**: 2.5235897435897434

```python
    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")
    
```

### 39. test_context_bundle_builder
- **File**: test_context_bundle_builder.py
- **Priority**: critical
- **Relevance Score**: 2.523076923076923

```python
def test_context_bundle_builder():
    """Test the ContextBundleBuilder implementation."""
    print("🏗️ Testing ContextBundleBuilder Component")
    print("=" * 60)
    
    try:
        # Import required components
        from context_bundle_builder import ContextBundleBuilder, EntityScoreBreakdown, EnhancedContextEntity
        from iterative_analysis_engine import AnalysisMemory, ConfidenceTracker
        
        print("✅ Successfully imported ContextBundleBuilder components")
        
        # Create mock IR data for testing
        mock_ir_data = {
            'modules': [
                {
                    'name': 'test_module',
                    'file': 'test_module.py',
                    'entities': [
                        {
                            'name': 'test_function',
                            'type': 'function',
                            'calls': ['helper_function'],
                            'used_by': ['main_function'],
                            'side_effects': [],
                            'errors': [],
                            'criticality': 'high',
                            'change_risk': 'medium',
                            'doc': 'Test function for demonstration'
                        },
                        {
                            'name': 'helper_function',
                            'type': 'function',
                            'calls': [],
                            'used_by': ['test_function'],
                            'side_effects': ['file_write'],
                            'errors': [],
                            'criticality': 'medium',
                            'change_risk': 'low',
                            'doc': ''
                        },
                        {
                            'name': 'TestClass',
                            'type': 'class',
                            'calls': ['test_function'],
                            'used_by': [],
                            'side_effects': [],
                            'errors': ['potential_memory_leak'],
                            'criticality': 'high',
                            'change_risk': 'high',
                            'doc': 'Test class with comprehensive documentation'
                        }
                    ]
                }
            ]
        }
        
        # Initialize components
        analysis_memory = AnalysisMemory()
        confidence_tracker = ConfidenceTracker()
        
        # Create ContextBundleBuilder
        builder = ContextBundleBuilder(
            ir_data=mock_ir_data,
            analysis_memory=analysis_memory,
            confidence_tracker=confidence_tracker,
            token_budget=4000
        )
        
        print("✅ ContextBundleBuilder initialized successfully")
        
        # Test different scenarios
        test_scenarios = [
            {
                "name": "Debugging Task",
                "task": "Debug memory leak in test function",
                "task_type": "debugging",
                "focus_entities": ["memory", "test"]
            },
            {
                "name": "Feature Development",
                "task": "Add new helper functionality",
                "task_type": "feature_development",
                "focus_entities": ["helper", "function"]
            },
            {
                "name": "Documentation Task",
                "task": "Improve documentation coverage",
                "task_type": "documentation",
                "focus_entities": ["doc", "documentation"]
            }
        ]
        
        all_results = {}
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n{'='*50}")
            print(f"🎯 Test Scenario {i}: {scenario['name']}")
            print(f"{'='*50}")
            
            start_time = time.time()
            
            # Build enhanced context bundle
            enhanced_bundle = builder.build(
                task=scenario['task'],
                task_type=scenario['task_type'],
                focus_entities=scenario['focus_entities']
            )
            
            end_time = time.time()
            build_time = end_time - start_time
            
            # Store results
            all_results[scenario['name']] = builder.to_dict(enhanced_bundle)
            
            print(f"✅ Context bundle built in {build_time:.3f} seconds")
            print(f"📊 Results Summary:")
            print(f"   • Selected entities: {len(enhanced_bundle.context_bundle)}")
            print(f"   • Token estimate: {enhanced_bundle.token_estimate}")
            print(f"   • Selected by: {enhanced_bundle.selected_by}")
            
            # Show score distribution
            print(f"\n📈 Score Distribution:")
            for score_range, count in enhanced_bundle.score_distribution.items():
                if count > 0:
                    print(f"   • {score_range}: {count} entities")
            
            # Show selection rationale
            print(f"\n💡 Selection Rationale:")
            rationale_lines = enhanced_bundle.selection_rationale.split('\n')
            for line in rationale_lines[:3]:  # Show first 3 lines
                if line.strip():
                    print(f"   {line.strip()}")
            
            # Show memory insights
            if enhanced_bundle.memory_insights:
                print(f"\n🧠 Memory Insights:")
                for insight in enhanced_bundle.memory_insights:
                    print(f"   • {insight}")
            
            # Show detailed entity scores
            print(f"\n🎯 Top Scoring Entities:")
            top_entities = sorted(enhanced_bundle.context_bundle, 
                                key=lambda e: e.total_score, reverse=True)[:3]
            
            for entity in top_entities:
                print(f"   • {entity.entity_name} (score: {entity.total_score:.2f})")
                breakdown = entity.score_breakdown
                print(f"     - Criticality: {breakdown.criticality:.2f}")
                print(f"     - Task relevance: {breakdown.task_relevance:.2f}")
                print(f"     - Confidence gap: {breakdown.confidence_gap:.2f}")
        
        # Test score breakdown functionality
        print(f"\n{'='*60}")
        print(f"🔍 Testing Score Breakdown Functionality")
        print(f"{'='*60}")
        
        # Test individual scoring components
        test_entity_data = {
            'name': 'test_scoring_function',
            'module_name': 'test_module',
            'type': 'function',
            'calls': ['dep1', 'dep2'],
            'used_by': ['caller1'],
            'criticality': 'high',
            'change_risk': 'medium',
            'doc': 'Well documented test function for scoring validation'
        }
        
        # Test detailed score calculation
        score_breakdown = builder._calculate_detailed_score(
            'test_module.test_scoring_function',
            test_entity_data,
            'Debug scoring function issues',
            'debugging',
            ['test', 'scoring']
        )
        
        print(f"✅ Score breakdown calculated:")
        print(f"   • Criticality: {score_breakdown.criticality:.3f}")
        print(f"   • Change risk: {score_breakdown.change_risk:.3f}")
        print(f"   • Task relevance: {score_breakdown.task_relevance:.3f}")
        print(f"   • Confidence gap: {score_breakdown.confidence_gap:.3f}")
        print(f"   • Dependency proximity: {score_breakdown.dependency_proximity:.3f}")
        print(f"   • Complexity: {score_breakdown.complexity:.3f}")
        print(f"   • Doc gap: {score_breakdown.doc_gap:.3f}")
        print(f"   • Historical relevance: {score_breakdown.historical_relevance:.3f}")
        print(f"   • TOTAL SCORE: {score_breakdown.total_score:.3f}")
        
        # Validate score is within expected range
        assert 0.0 <= score_breakdown.total_score <= 1.0, f"Score out of range: {score_breakdown.total_score}"
        print(f"✅ Score validation passed")
        
        # Generate comprehensive test report
        print(f"\n{'='*60}")
        print(f"📋 CONTEXTBUNDLEBUILDER TEST REPORT")
        print(f"{'='*60}")
        
        successful_tests = len(test_scenarios)
        total_entities_selected = sum(len(result['context_bundle']) for result in all_results.values())
        
        print(f"✅ All tests passed: {successful_tests}/{len(test_scenarios)}")
        print(f"📊 Aggregate Statistics:")
        print(f"   • Total entities selected across tests: {total_entities_selected}")
        print(f"   • Average entities per test: {total_entities_selected / len(test_scenarios):.1f}")
        
        # Test quality metrics
        print(f"\n🎯 Quality Metrics:")
        
        # Check if different task types produce different selections
        task_type_results = {}
        for scenario_name, result in all_results.items():
            task_type = result['task_type']
            entity_count = len(result['context_bundle'])
            task_type_results[task_type] = entity_count
        
        print(f"   • Task-specific selection variation:")
        for task_type, count in task_type_results.items():
            print(f"     - {task_type}: {count} entities")
        
        # Check score distribution variety
        has_score_variety = False
        for result in all_results.values():
            score_dist = result['score_distribution']
            non_zero_ranges = sum(1 for count in score_dist.values() if count > 0)
            if non_zero_ranges > 1:
                has_score_variety = True
                break
        
        print(f"   • Score distribution variety: {'✅ Yes' if has_score_variety else '❌ No'}")
        
        # Save detailed results
        output_file = "context_bundle_builder_test_results.json"
        with open(output_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        print(f"\n💾 Detailed results saved to: {output_file}")
        
        print(f"\n🎉 CONTEXTBUNDLEBUILDER TESTS: ALL PASSED!")
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("💡 Make sure ContextBundleBuilder module is available")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        import traceback
        traceback.print_exc()
        return False


```

### 40. test_ir_context_request
- **File**: test_ir_context_request.py
- **Priority**: critical
- **Relevance Score**: 2.523076923076923

```python
def test_ir_context_request():
    """Test the IR_CONTEXT_REQUEST functionality."""

    print("🧠 Testing IR_CONTEXT_REQUEST Integration with Caching")
    print("=" * 60)

    # Add the aider-main directory to the path
    aider_main_path = os.path.join(os.path.dirname(__file__), 'aider-main')
    if aider_main_path not in sys.path:
        sys.path.insert(0, aider_main_path)

    try:
        # Import the required modules
        from aider.context_request import ContextRequestHandler, IRContextRequest

        print("✅ Successfully imported IR context request modules")

        # Create a context request handler
        project_path = os.getcwd()
        handler = ContextRequestHandler(project_path)

        print(f"✅ Created ContextRequestHandler for project: {project_path}")

        # Test IR preloading
        print("\n🚀 Testing IR Preloading")
        print("   This should generate IR data once and cache it for subsequent requests...")

        # Preload IR data
        ir_data = ContextRequestHandler.preload_ir_data(project_path)
        if ir_data:
            print("✅ IR data preloaded successfully!")
        else:
            print("❌ IR preloading failed")
        
        # Test 1: Basic IR context request (should use cached IR data)
        print("\n🔍 Test 1: Basic IR Context Request (Using Cache)")

        ir_request = IRContextRequest(
            user_query="How does the intelligent context selection work?",
            task_description="Understand the intelligent context selection engine implementation",
            task_type="general_analysis",
            focus_entities=["context", "selection", "intelligent"],
            max_tokens=4000,
            include_ir_slices=True,
            include_code_context=True
        )

        print(f"   Task: {ir_request.task_description}")
        print(f"   Type: {ir_request.task_type}")
        print(f"   Focus: {ir_request.focus_entities}")
        print("   Expected: Should use cached IR data (fast)")

        # Process the IR context request
        import time
        start_time = time.time()
        result = handler.process_ir_context_request(ir_request)
        processing_time = time.time() - start_time
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return False

        print(f"✅ IR Context Request processed successfully in {processing_time:.2f} seconds!")
        if processing_time < 5:
            print("🚀 Fast processing - IR cache is working!")
        
        # Display results
        context_bundle = result.get("context_bundle", {})
        summary = result.get("summary", {})
        
        print(f"\n📊 Results Summary:")
        print(f"   Selected Entities: {context_bundle.get('total_entities', 0)}")
        print(f"   Token Utilization: {summary.get('token_utilization', 'N/A')}")
        print(f"   Critical Entities: {summary.get('critical_entities', 0)}")
        print(f"   Files Involved: {summary.get('files_involved', 0)}")
        
        # Show selection rationale
        rationale = context_bundle.get("selection_rationale", "")
        if rationale:
            print(f"\n🎯 Selection Rationale:")
            print(f"   {rationale}")
        
        # Show IR slices preview
        ir_slices = result.get("ir_slices", [])
        if ir_slices:
            print(f"\n📋 IR Slices Preview ({len(ir_slices)} total):")
            for i, slice_data in enumerate(ir_slices[:3]):
                print(f"   {i+1}. {slice_data['entity_name']} ({slice_data['entity_type']})")
                print(f"      File: {slice_data['file_path']}")
                print(f"      Criticality: {slice_data['criticality']}")
                print(f"      Priority: {slice_data['priority']}")
        
        # Show code context preview
        code_context = result.get("code_context", [])
        if code_context:
            print(f"\n💻 Code Context Preview ({len(code_context)} total):")
            for i, ctx in enumerate(code_context[:2]):
                print(f"   {i+1}. {ctx['entity_name']}")
                print(f"      File: {ctx['file_path']}")
                print(f"      Priority: {ctx['priority']}")
                print(f"      Code length: {len(ctx['source_code'])} characters")
        
        # Test 2: Debugging-focused request
        print("\n🐛 Test 2: Debugging-Focused IR Context Request (Using Cache)")

        debug_request = IRContextRequest(
            user_query="Find potential bugs in the context selection logic",
            task_description="Debug context selection issues and identify error-prone code",
            task_type="debugging",
            focus_entities=["error", "exception", "bug"],
            max_tokens=6000,
            include_ir_slices=True,
            include_code_context=True
        )

        start_time = time.time()
        debug_result = handler.process_ir_context_request(debug_request)
        debug_time = time.time() - start_time
        
        if "error" not in debug_result:
            debug_summary = debug_result.get("summary", {})
            print(f"   Debug Context Selected: {debug_summary.get('critical_entities', 0)} critical entities")
            print(f"   Token Utilization: {debug_summary.get('token_utilization', 'N/A')}")
            print(f"✅ Debugging context request successful in {debug_time:.2f} seconds!")
            if debug_time < 5:
                print("🚀 Fast processing - IR cache is working!")
        else:
            print(f"❌ Debug request error: {debug_result['error']}")
        
        # Test 3: Feature development request
        print("\n🚀 Test 3: Feature Development IR Context Request (Using Cache)")

        feature_request = IRContextRequest(
            user_query="How to extend the system with new analysis capabilities?",
            task_description="Understand extension points for adding new analysis features",
            task_type="feature_development",
            focus_entities=["analyzer", "extension", "plugin"],
            max_tokens=8000,
            include_ir_slices=True,
            include_code_context=True
        )

        start_time = time.time()
        feature_result = handler.process_ir_context_request(feature_request)
        feature_time = time.time() - start_time

        if "error" not in feature_result:
            feature_summary = feature_result.get("summary", {})
            print(f"   Feature Context Selected: {feature_summary.get('critical_entities', 0)} critical entities")
            print(f"   Token Utilization: {feature_summary.get('token_utilization', 'N/A')}")
            print(f"✅ Feature development context request successful in {feature_time:.2f} seconds!")
            if feature_time < 5:
                print("🚀 Fast processing - IR cache is working!")
        else:
            print(f"❌ Feature request error: {feature_result['error']}")

        print("\n🎉 All IR_CONTEXT_REQUEST tests completed successfully!")
        print(f"\n⚡ Performance Summary:")
        print(f"   Test 1 (General): {processing_time:.2f}s")
        print(f"   Test 2 (Debug): {debug_time:.2f}s")
        print(f"   Test 3 (Feature): {feature_time:.2f}s")
        print(f"   Average: {(processing_time + debug_time + feature_time) / 3:.2f}s")

        if all(t < 5 for t in [processing_time, debug_time, feature_time]):
            print("🚀 Excellent! All requests completed in under 5 seconds thanks to IR caching!")
        print("\n📝 Usage Instructions:")
        print("   To use in practice, LLM should send:")
        print('   {IR_CONTEXT_REQUEST: {')
        print('     "user_query": "Your question here",')
        print('     "task_description": "What you want to accomplish",')
        print('     "task_type": "debugging|feature_development|general_analysis",')
        print('     "focus_entities": ["keyword1", "keyword2"],')
        print('     "max_tokens": 8000')
        print('   }}')
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure the aider-main directory exists and contains the required modules")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 41. extract_enhanced_context
- **File**: enhanced_surgical_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.521153846153846

```python
    def extract_enhanced_context(self, project_path: str, symbol_name: str, file_path: str) -> Optional[EnhancedCodeContext]:
        """
        Extract enhanced code context for a symbol, combining complete implementation and usage contexts.

        Args:
            project_path: Path to the project root
            symbol_name: Name of the symbol to extract
            file_path: Path to the file containing the symbol

        Returns:
            EnhancedCodeContext object or None if extraction failed
        """
        cache_key = f"enhanced_context:{project_path}:{file_path}:{symbol_name}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        # Get symbol information
        symbols = self.file_extractor.get_symbols_in_file(project_path, file_path)
        if not symbols:
            return None

        target_symbol = next((s for s in symbols if s.name == symbol_name), None)
        if not target_symbol:
            return None

        # Extract complete implementation
        extraction_range = self.file_extractor.extract_symbol_range(symbol_name, file_path, project_path)
        implementation = self.file_extractor.extract_symbol_content(symbol_name, file_path, project_path)

        # Extract essential imports
        essential_imports = self._extract_essential_imports(project_path, file_path)

        # Extract containing class signature if it's a method
        containing_class = self._extract_containing_class(project_path, file_path, target_symbol)

        # Extract usage contexts
        usage_contexts = self.context_extractor.extract_usage_contexts(project_path, symbol_name, file_path)

        # Create the enhanced context
        context = EnhancedCodeContext(
            symbol_name=symbol_name,
            file_path=file_path,
            symbol_type=target_symbol.symbol_type,
            complete_implementation=implementation,
            extraction_range=extraction_range,
            essential_imports=essential_imports,
            containing_class_signature=containing_class,
            usage_contexts=usage_contexts
        )

        # Cache the result
        self._update_cache(cache_key, context)

        return context

```

### 42. process_context_requests
- **File**: test_repo_map_compatibility.py
- **Priority**: critical
- **Relevance Score**: 2.521153846153846

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        # Check if we've reached the maximum number of context requests for this query
        if self.current_query_context_requests >= 3:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None

        # Detect if there's a context request in the content
        context_request = self.context_request_integration.detect_context_request(content)
        if not context_request:
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")

        # Get the repository overview
        repo_overview = ""
        if self.repo_map:
            try:
                # Try different methods to get the repository overview
                if hasattr(self.repo_map, 'get_repo_overview'):
                    repo_overview = self.repo_map.get_repo_overview()
                elif hasattr(self.repo_map, 'get_all_files') and callable(getattr(self.repo_map, 'get_all_files')):
                    try:
                        # Build a simple overview from the list of files
                        files = self.repo_map.get_all_files()
                        repo_overview = "\n".join(files)
                    except Exception as e:
                        self.io.tool_warning(f"Error calling get_all_files: {e}")
                elif hasattr(self.repo_map, 'files'):
                    # Build a simple overview from the files dictionary
                    repo_overview = "\n".join(self.repo_map.files.keys())
                else:
                    self.io.tool_warning("Repository map doesn't have a method to get an overview")
            except Exception as e:
                self.io.tool_warning(f"Error getting repository overview: {e}")

        # Process the context request and get the augmented prompt
        augmented_prompt = self.context_request_integration.process_context_request(
            context_request=context_request,
            original_user_query=user_message,
            repo_overview=repo_overview
        )

        # Update the conversation history in the context request integration
        self.context_request_integration.update_conversation_history("user", user_message)
        self.context_request_integration.update_conversation_history("assistant", content)

        # Clean up the content by removing the context request
        context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
        cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt


```

### 43. process_ir_context_requests
- **File**: aider-main\aider\coders\base_coder.py
- **Priority**: critical
- **Relevance Score**: 2.516153846153846

```python
    def process_ir_context_requests(self, content, user_message):
        """
        Process any IR context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if an IR context request was detected,
            or (content, None) if no IR context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("IR_CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Look for IR_CONTEXT_REQUEST pattern
        patterns = [
            r'\{IR_CONTEXT_REQUEST:\s*(.*?)\}\}',
            r'\{IR_CONTEXT_REQUEST:\s*(.*?)\}',
            r'\{IR_CONTEXT_REQUEST:\s*(.*)'
        ]

        match = None
        ir_request_pattern = None
        for pattern in patterns:
            match = re.search(pattern, content, re.DOTALL)
            if match:
                ir_request_pattern = pattern
                break

        if not match:
            return content, None

        try:
            # Extract and parse the IR context request
            request_json_str = match.group(1).strip()
            request_json = json.loads(request_json_str)

            # Create IRContextRequest object
            ir_request = IRContextRequest(
                user_query=request_json.get("user_query", user_message),
                task_description=request_json.get("task_description", user_message),
                task_type=request_json.get("task_type", "general_analysis"),
                focus_entities=request_json.get("focus_entities", []),
                max_tokens=request_json.get("max_tokens", 8000),
                include_ir_slices=request_json.get("include_ir_slices", True),
                include_code_context=request_json.get("include_code_context", True)
            )

            # Log the IR context request
            self.io.tool_output(f"Processing IR context request: {ir_request.task_description}")

            # Initialize context request handler if not already done
            if not hasattr(self, 'context_request_handler') or self.context_request_handler is None:
                try:
                    from ..context_request import ContextRequestHandler

                    # Determine the correct project path
                    import os
                    project_path = os.getcwd()

                    # If current working directory is the aider repository itself,
                    # and we have files in the chat, use the common root of those files
                    if (os.path.basename(project_path) == 'aider' and
                        os.path.exists(os.path.join(project_path, 'aider-main')) and
                        (self.abs_fnames or self.abs_read_only_fnames)):
                        # Use the common root of the files in the chat
                        if self.abs_fnames:
                            project_path = utils.find_common_root(self.abs_fnames)
                        elif self.abs_read_only_fnames:
                            project_path = utils.find_common_root(self.abs_read_only_fnames)

                    self.context_request_handler = ContextRequestHandler(project_path)
                except Exception as e:
                    self.io.tool_error(f"Failed to initialize context request handler: {e}")
                    return content, None

            # Clean up the content by removing the IR context request
            cleaned_content = re.sub(ir_request_pattern, "", content, flags=re.DOTALL).strip()

            # Process the IR context request
            result = self.context_request_handler.process_ir_context_request(ir_request)

            if "error" in result:
                self.io.tool_error(f"Error processing IR context request: {result['error']}")
                return cleaned_content, None

            # Display the IR context response to the user
            self._display_ir_context_response_to_user(result, ir_request)

            # Create augmented prompt for the LLM
            augmented_prompt = self._create_ir_context_augmented_prompt(result, user_message)

            return cleaned_content, augmented_prompt

        except json.JSONDecodeError as e:
            self.io.tool_error(f"Invalid JSON in IR context request: {e}")
            return content, None
        except Exception as e:
            self.io.tool_error(f"Error processing IR context request: {e}")
            return content, None

```

### 44. _extract_definition_info
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.51525641025641

```python
    def _extract_definition_info(self, content: str, line_num: int, symbol_name: str) -> Tuple[DefinitionType, Optional[str], Optional[str]]:
        """
        Extract information about a symbol definition.

        Args:
            content: The file content
            line_num: The line number where the symbol is defined (1-based)
            symbol_name: The name of the symbol

        Returns:
            A tuple of (definition_type, signature, docstring)
        """
        lines = content.splitlines()
        if line_num < 1 or line_num > len(lines):
            return DefinitionType.UNKNOWN, None, None

        line = lines[line_num - 1]

        # Determine definition type
        if re.search(rf'class\s+{re.escape(symbol_name)}', line):
            definition_type = DefinitionType.CLASS
            # Extract class signature
            signature = line.strip()

            # Look for docstring
            docstring = self._extract_docstring(lines, line_num)

            return definition_type, signature, docstring

        elif re.search(rf'def\s+{re.escape(symbol_name)}', line):
            # Check if this is a method (indented and/or has self parameter)
            if line.startswith(' ') or re.search(r'\(\s*self\s*[,)]', line):
                definition_type = DefinitionType.METHOD
            else:
                definition_type = DefinitionType.FUNCTION

            # Extract function signature
            signature = line.strip()
            if not signature.endswith(':'):
                # Look for continuation lines
                i = line_num
                while i < len(lines) and not signature.endswith(':'):
                    i += 1
                    if i < len(lines):
                        signature += ' ' + lines[i].strip()

            # Look for docstring
            docstring = self._extract_docstring(lines, line_num)

            return definition_type, signature, docstring

        elif re.search(rf'{re.escape(symbol_name)}\s*=', line):
            # Check if this is a constant (all uppercase)
            if symbol_name.isupper():
                definition_type = DefinitionType.CONSTANT
            else:
                definition_type = DefinitionType.VARIABLE

            # Extract variable assignment
            signature = line.strip()

            return definition_type, signature, None

        else:
            return DefinitionType.UNKNOWN, None, None

```

### 45. debug_context_request_paths
- **File**: debug_context_request_path.py
- **Priority**: critical
- **Relevance Score**: 2.5147435897435897

```python
def debug_context_request_paths():
    """Debug the path resolution in context request system."""
    print("🔍 Debugging Context Request Path Resolution")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, SymbolRequest
        
        # Get current working directory
        current_dir = os.getcwd()
        print(f"📁 Current working directory: {current_dir}")
        
        # Check what the context request handler thinks the project path is
        handler = ContextRequestHandler(current_dir)
        print(f"📁 Handler project path: {handler.project_path}")
        
        # Create a test symbol request
        symbol = SymbolRequest(
            type="method_definition",
            name="close_position_based_on_conditions",
            file_hint="trade_management/position_exit_manager.py"
        )
        
        print(f"\n🎯 Testing symbol request:")
        print(f"  Symbol name: {symbol.name}")
        print(f"  File hint: {symbol.file_hint}")
        
        # Test the file path construction
        expected_full_path = os.path.join(handler.project_path, symbol.file_hint)
        print(f"\n📂 Expected full path: {expected_full_path}")
        print(f"📂 File exists: {os.path.exists(expected_full_path)}")
        
        # Check if the directory exists
        expected_dir = os.path.dirname(expected_full_path)
        print(f"📂 Directory exists: {os.path.exists(expected_dir)}")
        
        # List what's actually in the project directory
        print(f"\n📋 Contents of project directory ({handler.project_path}):")
        try:
            items = os.listdir(handler.project_path)
            for item in sorted(items)[:20]:  # Show first 20 items
                item_path = os.path.join(handler.project_path, item)
                item_type = "📁" if os.path.isdir(item_path) else "📄"
                print(f"  {item_type} {item}")
            if len(items) > 20:
                print(f"  ... and {len(items) - 20} more items")
        except Exception as e:
            print(f"  ❌ Error listing directory: {e}")
        
        # Check if there's a trade_management directory anywhere
        print(f"\n🔍 Searching for 'trade_management' directory...")
        found_trade_dirs = []
        for root, dirs, files in os.walk(handler.project_path):
            if 'trade_management' in dirs:
                trade_dir = os.path.join(root, 'trade_management')
                found_trade_dirs.append(trade_dir)
                print(f"  📁 Found: {trade_dir}")
        
        if not found_trade_dirs:
            print(f"  ❌ No 'trade_management' directory found in {handler.project_path}")
        
        # Check if there are any Python files with 'position' in the name
        print(f"\n🔍 Searching for files with 'position' in the name...")
        found_position_files = []
        for root, dirs, files in os.walk(handler.project_path):
            for file in files:
                if 'position' in file.lower() and file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, handler.project_path)
                    found_position_files.append(rel_path)
                    print(f"  📄 Found: {rel_path}")
        
        if not found_position_files:
            print(f"  ❌ No files with 'position' in name found")
        
        # Test the actual _find_file_for_symbol method
        print(f"\n🧪 Testing _find_file_for_symbol method...")
        try:
            found_file = handler._find_file_for_symbol(symbol)
            print(f"  Result: {found_file}")
            if found_file:
                print(f"  ✅ File found successfully!")
            else:
                print(f"  ❌ File not found by handler")
        except Exception as e:
            print(f"  ❌ Error in _find_file_for_symbol: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging context request paths: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 46. test_context_request_code_block_closure
- **File**: test_context_request_code_block_fix.py
- **Priority**: critical
- **Relevance Score**: 2.5147435897435897

```python
def test_context_request_code_block_closure():
    """
    Test that the markdown stream is properly closed when processing CONTEXT_REQUEST
    to prevent follow-up responses from appearing inside the code block.
    """
    
    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput
        
        print("=== CONTEXT_REQUEST CODE BLOCK CLOSURE TEST ===")
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        coder = Coder.create(main_model=model, io=io, fnames=[])
        
        # Simulate a response with CONTEXT_REQUEST
        test_content = """I need to see the implementation details.

{CONTEXT_REQUEST: {
  "original_user_query_context": "User asking about function implementation",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "TestClass.test_method", "file_hint": "test_file.py"}
  ],
  "reason_for_request": "Need to understand the implementation"
}}

This should not appear inside the code block."""
        
        # Test the process_context_requests method
        user_message = "Show me how the test method works"
        
        # Check if the method exists and can be called
        if hasattr(coder, 'process_context_requests'):
            print("✅ process_context_requests method found")
            
            # Test the content cleaning
            cleaned_content, context_prompt = coder.process_context_requests(test_content, user_message)
            
            print(f"Original content length: {len(test_content)}")
            print(f"Cleaned content length: {len(cleaned_content) if cleaned_content else 0}")
            
            # Check that CONTEXT_REQUEST was removed from cleaned content
            if cleaned_content and "CONTEXT_REQUEST" not in cleaned_content:
                print("✅ CONTEXT_REQUEST properly removed from cleaned content")
            else:
                print("❌ CONTEXT_REQUEST not properly removed from cleaned content")
                return False
            
            # Check that the remaining content is preserved
            if cleaned_content and "This should not appear inside the code block." in cleaned_content:
                print("✅ Follow-up content preserved in cleaned response")
            else:
                print("❌ Follow-up content not preserved")
                return False
            
            # Check if context prompt was generated (indicates CONTEXT_REQUEST was detected)
            if context_prompt:
                print("✅ Context prompt generated (CONTEXT_REQUEST detected)")
            else:
                print("⚠️  No context prompt generated (CONTEXT_REQUEST not detected or not available)")
                # This might be expected in test environment
                return True
            
        else:
            print("❌ process_context_requests method not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 47. get_focused_inheritance_context
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.5128205128205128

```python
    def get_focused_inheritance_context(self, project_path: str, class_name: str,
                                      file_path: str) -> Dict:
        """
        Get focused context around class inheritance relationships.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class

        Returns:
            A dictionary with inheritance context information
        """
        extractor = self._get_context_extractor()
        inheritance_map = extractor.get_focused_inheritance_context(project_path, class_name, file_path)

        # Convert to a serializable dictionary
        result = {
            'class_name': inheritance_map.class_name,
            'file_path': inheritance_map.file_path,
            'base_classes': [],
            'derived_classes': [],
            'implementation_contexts': []
        }

        # Add base classes
        for base_ctx in inheritance_map.base_classes:
            result['base_classes'].append({
                'file_path': base_ctx.snippet.file_path,
                'start_line': base_ctx.snippet.start_line,
                'end_line': base_ctx.snippet.end_line,
                'content': base_ctx.snippet.content,
                'symbol_name': base_ctx.snippet.symbol_name,
                'signature': base_ctx.signature,
                'docstring': base_ctx.docstring
            })

        # Add derived classes
        for derived_ctx in inheritance_map.derived_classes:
            result['derived_classes'].append({
                'file_path': derived_ctx.snippet.file_path,
                'start_line': derived_ctx.snippet.start_line,
                'end_line': derived_ctx.snippet.end_line,
                'content': derived_ctx.snippet.content,
                'symbol_name': derived_ctx.snippet.symbol_name,
                'signature': derived_ctx.signature,
                'docstring': derived_ctx.docstring
            })

        # Add implementation contexts
        for impl_ctx in inheritance_map.implementation_contexts:
            result['implementation_contexts'].append({
                'file_path': impl_ctx.file_path,
                'start_line': impl_ctx.start_line,
                'end_line': impl_ctx.end_line,
                'content': impl_ctx.content,
                'symbol_name': impl_ctx.symbol_name,
                'surrounding_function': impl_ctx.surrounding_function
            })

        return result

```

### 48. test_context_request_end_to_end
- **File**: test_context_request_end_to_end.py
- **Priority**: critical
- **Relevance Score**: 2.5128205128205128

```python
def test_context_request_end_to_end():
    """Test the complete CONTEXT_REQUEST workflow."""
    print("🧪 Testing CONTEXT_REQUEST End-to-End")
    print("=" * 60)

    mock_project = None
    try:
        # Create mock trading project
        mock_project = create_mock_trading_project()
        print(f"📁 Created mock project: {mock_project}")

        from aider.context_request.context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest
        from aider.context_request.aider_integration_service import AiderIntegrationService

        # Create context request handler
        aider_service = AiderIntegrationService()
        handler = ContextRequestHandler(mock_project, aider_service)

        print(f"✅ Handler created successfully")

        # Create the exact CONTEXT_REQUEST from the user scenario
        context_request = ContextRequest(
            original_user_query_context="how does the close_position_based_on_conditions function work?",
            reason_for_request="User wants to understand the implementation of position closing logic",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="trade_management/position_exit_manager.py"
                ),
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="services/position_observer.py"
                )
            ]
        )

        print(f"🎯 Processing CONTEXT_REQUEST:")
        print(f"   Query: {context_request.original_user_query_context}")
        print(f"   Symbols: {len(context_request.symbols_of_interest)}")

        # Process the context request
        result = handler.process_context_request(context_request)

        print(f"\n📊 Results:")
        print(f"   Extracted symbols: {len(result['extracted_symbols'])}")
        print(f"   Not found symbols: {len(result['not_found_symbols'])}")

        # Verify results
        success_criteria = [
            (len(result['extracted_symbols']) > 0, "At least one symbol extracted"),
            (len(result['not_found_symbols']) > 0, "Some symbols not found (expected)"),
            (result['original_user_query_context'] == context_request.original_user_query_context, "Query context preserved"),
        ]

        passed = 0
        total = len(success_criteria)

        for condition, description in success_criteria:
            status = "✅" if condition else "❌"
            print(f"   {status} {description}")
            if condition:
                passed += 1

        # Show detailed results
        if result['extracted_symbols']:
            print(f"\n📋 Successfully extracted symbols:")
            for symbol in result['extracted_symbols']:
                print(f"   ✅ {symbol['symbol_name']} from {symbol['file_path']}")
                print(f"      Content length: {len(symbol['content'])} characters")
                print(f"      Preview: {symbol['content'][:100]}...")

        if result['not_found_symbols']:
            print(f"\n⚠️  Symbols not found (expected):")
            for symbol in result['not_found_symbols']:
                print(f"   ❌ {symbol['symbol_name']}: {symbol['reason']}")

        print(f"\n📊 End-to-end test: {passed}/{total} criteria passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error in end-to-end test: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        # Clean up mock project
        if mock_project and os.path.exists(mock_project):
            shutil.rmtree(mock_project)
            print(f"🧹 Cleaned up mock project")

```

### 49. extract_definition_contexts
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.5119230769230767

```python
    def extract_definition_contexts(self, project_path: str, symbols: List[str],
                                   source_file: str) -> List[DefinitionContext]:
        """
        For symbols referenced by a file, extract their definitions with surrounding context.

        Args:
            project_path: Path to the project root
            symbols: List of symbol names to find definitions for
            source_file: Path to the file that references these symbols

        Returns:
            A list of DefinitionContext objects
        """
        cache_key = f"definition_contexts:{project_path}:{','.join(symbols)}:{source_file}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        definition_contexts = []

        for symbol in symbols:
            # Find the file that defines this symbol
            defining_file = self.aider_service.find_file_defining_symbol(project_path, symbol)
            if not defining_file:
                continue

            # Find line numbers where the symbol is defined
            line_numbers = self._find_definition_line_numbers(project_path, defining_file, symbol)

            for line_num in line_numbers:
                # Read the file content
                content = self._read_file_content(project_path, defining_file)
                if not content:
                    continue

                # Determine the definition type
                definition_type, signature, docstring = self._extract_definition_info(
                    content, line_num, symbol)

                # Determine appropriate context window size
                adjusted_window = self._determine_context_window_size(
                    content, line_num - 1,
                    "class" if definition_type == DefinitionType.CLASS else "function")

                # Extract the code snippet
                snippet = self._extract_code_snippet(
                    project_path, defining_file, line_num, adjusted_window,
                    ContextType.DEFINITION, symbol)

                if snippet:
                    definition_context = DefinitionContext(
                        snippet=snippet,
                        definition_type=definition_type,
                        signature=signature,
                        docstring=docstring
                    )
                    definition_contexts.append(definition_context)

        # Cache the result
        self._update_cache(cache_key, definition_contexts)

        return definition_contexts

```

### 50. send_message
- **File**: test_context_request_hang.py
- **Priority**: critical
- **Relevance Score**: 2.5119230769230767

```python
    def send_message(self, user_message):
        """
        Simulate sending a message to the LLM and processing the response.
        
        Args:
            user_message: The user message
            
        Returns:
            The processed response
        """
        # Simulate an LLM response with a context request
        llm_response = f"""
I need to understand how the surgical file extractor works to answer your question properly.

{{CONTEXT_REQUEST: {{
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {{"type": "method_definition", "name": "SurgicalFileExtractor.extract_symbol_content", "file_hint": "surgical_file_extractor.py"}}
  ],
  "reason_for_request": "To understand how the surgical file extractor works"
}}}}
"""
        
        # Process the context request
        cleaned_content, augmented_prompt = self.process_context_requests(llm_response, user_message)
        
        # Check if a context request was detected
        if augmented_prompt:
            self.io.tool_output("Context request detected and processed!")
            self.io.tool_output("Augmented prompt generated.")
            
            # Simulate sending the augmented prompt to the LLM
            self.io.tool_output("Sending augmented prompt to LLM...")
            
            # Simulate a delay for the LLM to process the augmented prompt
            time.sleep(1)
            
            # Simulate a new LLM response
            new_llm_response = """
The SurgicalFileExtractor is a specialized tool designed to extract specific code symbols (like functions, methods, or classes) from files in a project. Here's how it works:

1. The extract_symbol_content method is the main entry point for extracting a symbol's content from a file.
2. It first calls extract_symbol_range to determine the line range of the symbol in the file.
3. It then reads the file content and extracts the lines corresponding to the symbol.
4. The extracted content is returned as a string.

This surgical extraction approach allows for more targeted and efficient code analysis compared to loading entire files.
"""
            
            # Add the new response to the conversation history
            self.context_request_integration.update_conversation_history("assistant", new_llm_response)
            
            return new_llm_response
        else:
            self.io.tool_output("No context request detected.")
            return llm_response


```

### 51. process_context_requests
- **File**: test_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.508076923076923

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        # Check if we've reached the maximum number of context requests for this query
        if self.current_query_context_requests >= 3:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None

        # Detect if there's a context request in the content
        context_request = self.context_request_integration.detect_context_request(content)
        if not context_request:
            return content, None

        # Increment the context request counter
        self.current_query_context_requests += 1

        # Log the context request
        self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")

        # Get the repository overview
        repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
"""

        # Process the context request and get the augmented prompt
        augmented_prompt = self.context_request_integration.process_context_request(
            context_request=context_request,
            original_user_query=user_message,
            repo_overview=repo_overview
        )

        # Update the conversation history in the context request integration
        self.context_request_integration.update_conversation_history("user", user_message)
        self.context_request_integration.update_conversation_history("assistant", content)

        # Clean up the content by removing the context request
        context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
        cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)

        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt


```

### 52. build
- **File**: context_bundle_builder.py
- **Priority**: critical
- **Relevance Score**: 2.506923076923077

```python
    def build(self, task: str, task_type: str = "general_analysis", 
              focus_entities: Optional[List[str]] = None) -> EnhancedContextBundle:
        """
        Build an enhanced context bundle with detailed scoring.
        
        Args:
            task: Task description
            task_type: Type of analysis task
            focus_entities: Optional focus entities
            
        Returns:
            EnhancedContextBundle with detailed metadata
        """
        print(f"🏗️ Building enhanced context bundle for: {task}")
        
        # Step 1: Score all entities
        scored_entities = self._score_all_entities(task, task_type, focus_entities)
        
        # Step 2: Select entities within budget
        selected_entities = self._select_entities_within_budget(scored_entities)
        
        # Step 3: Build enhanced context bundle
        context_bundle = self._build_enhanced_bundle(
            task, task_type, selected_entities, scored_entities
        )
        
        print(f"✅ Enhanced context bundle built:")
        print(f"   Selected entities: {len(context_bundle.context_bundle)}")
        print(f"   Token estimate: {context_bundle.token_estimate}")
        print(f"   Score distribution: {context_bundle.score_distribution}")
        
        return context_bundle
    
```

### 53. test_message_construction_deep_dive
- **File**: test_hidden_context_detection.py
- **Priority**: critical
- **Relevance Score**: 2.506923076923077

```python
def test_message_construction_deep_dive():
    """Deep dive into how messages are constructed to find hidden repository content."""
    print("\n🔍 Message Construction Deep Dive")
    print("=" * 80)
    
    try:
        from aider.coders.base_coder import Coder, SMART_MAP_REQUEST_AVAILABLE
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repo import GitRepo
        
        if not SMART_MAP_REQUEST_AVAILABLE:
            print("❌ Smart Map Request System not available")
            return False
        
        # Create a coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        repo = GitRepo(io, "aider-main", "aider-main")
        
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=20000,
            repo=repo
        )
        
        # Test all message construction methods
        message_methods = [
            ("get_system_message", lambda: coder.get_system_message()),
            ("get_repo_messages", lambda: coder.get_repo_messages()),
            ("get_readonly_files_messages", lambda: coder.get_readonly_files_messages()),
        ]
        
        hidden_content_found = False
        
        for method_name, method_call in message_methods:
            print(f"\n🧪 Testing {method_name}:")
            try:
                result = method_call()
                
                if result is None:
                    print(f"   ✅ {method_name} returns None")
                elif isinstance(result, list):
                    total_chars = 0
                    for i, msg in enumerate(result):
                        if isinstance(msg, dict) and 'content' in msg:
                            content = msg['content']
                            content_length = len(content)
                            total_chars += content_length
                            
                            print(f"   Message {i+1}: {content_length} characters")
                            
                            if content_length > 5000:
                                print(f"     ❌ LARGE CONTENT DETECTED!")
                                print(f"     Preview: {content[:200]}...")
                                hidden_content_found = True
                    
                    print(f"   Total characters: {total_chars}")
                    if total_chars > 10000:
                        print(f"   ❌ EXCESSIVE TOTAL CONTENT!")
                        hidden_content_found = True
                        
                elif isinstance(result, str):
                    content_length = len(result)
                    print(f"   String result: {content_length} characters")
                    
                    if content_length > 5000:
                        print(f"   ❌ LARGE STRING CONTENT!")
                        print(f"   Preview: {result[:200]}...")
                        hidden_content_found = True
                else:
                    print(f"   ✅ {method_name} returns {type(result)}")
                    
            except Exception as e:
                print(f"   ❌ {method_name} failed: {e}")
        
        return not hidden_content_found
        
    except Exception as e:
        print(f"❌ Error in message construction deep dive: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 54. _update_cache
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.5035897435897434

```python
    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value."""
        self.cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

```

### 55. get_focused_inheritance_context
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.503076923076923

```python
    def get_focused_inheritance_context(self, project_path: str, class_name: str,
                                      file_path: str) -> InheritanceContextMap:
        """
        Get focused context around class inheritance relationships.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class

        Returns:
            An InheritanceContextMap object
        """
        # Get base classes
        base_classes_info = self.aider_service.get_base_classes_of(project_path, class_name, file_path)

        # Get derived classes
        derived_classes_info = self.aider_service.get_derived_classes_of(project_path, class_name, file_path)

        # Extract definition contexts for base classes
        base_class_contexts = []
        for base_class_info in base_classes_info:
            base_class_name = base_class_info.get('class_name')
            base_class_file = base_class_info.get('file_path')

            if base_class_name and base_class_file:
                # Find line numbers where the base class is defined
                line_numbers = self._find_definition_line_numbers(project_path, base_class_file, base_class_name)

                for line_num in line_numbers:
                    # Read the file content
                    content = self._read_file_content(project_path, base_class_file)
                    if not content:
                        continue

                    # Determine the definition type and extract info
                    definition_type, signature, docstring = self._extract_definition_info(
                        content, line_num, base_class_name)

                    # Determine appropriate context window size
                    adjusted_window = self._determine_context_window_size(
                        content, line_num - 1, "class")

                    # Extract the code snippet
                    snippet = self._extract_code_snippet(
                        project_path, base_class_file, line_num, adjusted_window,
                        ContextType.DEFINITION, base_class_name)

                    if snippet:
                        definition_context = DefinitionContext(
                            snippet=snippet,
                            definition_type=DefinitionType.CLASS,
                            signature=signature,
                            docstring=docstring
                        )
                        base_class_contexts.append(definition_context)

        # Extract definition contexts for derived classes
        derived_class_contexts = []
        for derived_class_info in derived_classes_info:
            derived_class_name = derived_class_info.get('class_name')
            derived_class_file = derived_class_info.get('file_path')

            if derived_class_name and derived_class_file:
                # Find line numbers where the derived class is defined
                line_numbers = self._find_definition_line_numbers(project_path, derived_class_file, derived_class_name)

                for line_num in line_numbers:
                    # Read the file content
                    content = self._read_file_content(project_path, derived_class_file)
                    if not content:
                        continue

                    # Determine the definition type and extract info
                    definition_type, signature, docstring = self._extract_definition_info(
                        content, line_num, derived_class_name)

                    # Determine appropriate context window size
                    adjusted_window = self._determine_context_window_size(
                        content, line_num - 1, "class")

                    # Extract the code snippet
                    snippet = self._extract_code_snippet(
                        project_path, derived_class_file, line_num, adjusted_window,
                        ContextType.DEFINITION, derived_class_name)

                    if snippet:
                        definition_context = DefinitionContext(
                            snippet=snippet,
                            definition_type=DefinitionType.CLASS,
                            signature=signature,
                            docstring=docstring
                        )
                        derived_class_contexts.append(definition_context)

        # Extract implementation contexts (method overrides, etc.)
        implementation_contexts = []

        # Find methods in the class
        class_line_numbers = self._find_definition_line_numbers(project_path, file_path, class_name)
        if class_line_numbers:
            content = self._read_file_content(project_path, file_path)
            if content:
                lines = content.splitlines()
                class_line = class_line_numbers[0] - 1  # Convert to 0-based

                # Find the indentation level of the class
                match = re.match(r'^(\s*)', lines[class_line])
                class_indent = match.group(1) if match else ''
                method_indent = class_indent + '    '  # Assuming 4 spaces for indentation

                # Look for method definitions
                for i in range(class_line + 1, len(lines)):
                    line = lines[i]
                    if line.startswith(method_indent) and 'def ' in line:
                        # This is a method definition
                        method_match = re.search(r'def\s+(\w+)', line)
                        if method_match:
                            method_name = method_match.group(1)

                            # Check if this method overrides a base class method
                            for base_class_info in base_classes_info:
                                base_class_name = base_class_info.get('class_name')
                                base_class_file = base_class_info.get('file_path')

                                if base_class_name and base_class_file:
                                    # Check if the base class has this method
                                    base_content = self._read_file_content(project_path, base_class_file)
                                    if base_content and re.search(rf'def\s+{method_name}\s*\(', base_content):
                                        # This is an overridden method
                                        adjusted_window = self._determine_context_window_size(
                                            content, i, "method")

                                        # Extract the code snippet
                                        snippet = self._extract_code_snippet(
                                            project_path, file_path, i + 1, adjusted_window,
                                            ContextType.IMPLEMENTATION, method_name)

                                        if snippet:
                                            implementation_contexts.append(snippet)

        return InheritanceContextMap(
            class_name=class_name,
            file_path=file_path,
            base_classes=base_class_contexts,
            derived_classes=derived_class_contexts,
            implementation_contexts=implementation_contexts
        )




```

### 56. _enhance_with_dependency_context
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.5016666666666665

```python
    def _enhance_with_dependency_context(self, selected_entities: List[ContextEntity],
                                       task_type: TaskType) -> List[ContextEntity]:
        """Enhance the selection by adding important dependency context."""
        enhanced = selected_entities.copy()
        selected_names = {f"{e.module_name}.{e.entity_name}" for e in selected_entities}

        # Calculate remaining token budget
        current_tokens = sum(e.token_estimate for e in enhanced)
        remaining_tokens = self.max_tokens - current_tokens

        # Add critical dependencies
        for entity in selected_entities:
            entity_name = f"{entity.module_name}.{entity.entity_name}"

            # Add entities that this one depends on (calls)
            dependencies = self.dependency_graph.get(entity_name, set())
            for dep_name in dependencies:
                if dep_name not in selected_names and dep_name in self.entity_map:
                    dep_entity = self.entity_map[dep_name]
                    if (remaining_tokens >= dep_entity.token_estimate and
                        dep_entity.criticality in ['high', 'medium']):
                        enhanced.append(dep_entity)
                        selected_names.add(dep_name)
                        remaining_tokens -= dep_entity.token_estimate

            # Add entities that depend on this one (reverse dependencies)
            reverse_deps = self.reverse_dependency_graph.get(entity_name, set())
            for rev_dep_name in reverse_deps:
                if rev_dep_name not in selected_names and rev_dep_name in self.entity_map:
                    rev_dep_entity = self.entity_map[rev_dep_name]
                    if (remaining_tokens >= rev_dep_entity.token_estimate and
                        rev_dep_entity.criticality in ['high', 'medium']):
                        enhanced.append(rev_dep_entity)
                        selected_names.add(rev_dep_name)
                        remaining_tokens -= rev_dep_entity.token_estimate

        return enhanced

```

### 57. process_context_requests
- **File**: test_full_aider_integration.py
- **Priority**: critical
- **Relevance Score**: 2.501153846153846

```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.
        
        Args:
            content: The LLM response content
            user_message: The original user message
            
        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        # Initialize the context request integration if not already done
        if not hasattr(self, 'context_request_integration') or self.context_request_integration is None:
            self.context_request_integration = AiderContextRequestIntegration(self.root)
        
        # Check if we've reached the maximum number of context requests for this query
        if self.current_query_context_requests >= 3:
            self.io.tool_error("Maximum number of context requests reached for this query.")
            return content, None
            
        # Detect if there's a context request in the content
        context_request = self.context_request_integration.detect_context_request(content)
        if not context_request:
            return content, None
            
        # Increment the context request counter
        self.current_query_context_requests += 1
        
        # Log the context request
        self.io.tool_output(f"Processing context request: {self.context_request_integration.get_context_request_summary(context_request)}")
        
        # Get the repository overview
        repo_overview = ""
        if self.repo_map:
            repo_overview = self.repo_map.get_repo_overview()
            
        # Process the context request and get the augmented prompt
        augmented_prompt = self.context_request_integration.process_context_request(
            context_request=context_request,
            original_user_query=user_message,
            repo_overview=repo_overview
        )
        
        # Update the conversation history in the context request integration
        self.context_request_integration.update_conversation_history("user", user_message)
        self.context_request_integration.update_conversation_history("assistant", content)
        
        # Clean up the content by removing the context request
        context_request_pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
        cleaned_content = re.sub(context_request_pattern, "", content, flags=re.DOTALL)
        
        # Return the cleaned content and the augmented prompt
        return cleaned_content, augmented_prompt


```

### 58. _display_ir_context_response_to_user
- **File**: aider-main\aider\coders\base_coder.py
- **Priority**: critical
- **Relevance Score**: 2.498974358974359

```python
    def _display_ir_context_response_to_user(self, result, ir_request):
        """
        Display the IR_CONTEXT_REQUEST response to the user in the chat.

        Args:
            result: The IR context result
            ir_request: The original IR context request
        """
        if not result:
            return

        # Create a user-friendly header
        header = f"# 🧠 IR_CONTEXT_REQUEST Response\n\n"
        header += f"**Task**: {ir_request.task_description}  \n"
        header += f"**Type**: {ir_request.task_type}  \n"
        header += f"**Max Tokens**: {ir_request.max_tokens}  \n"
        header += f"**Focus Entities**: {', '.join(ir_request.focus_entities) if ir_request.focus_entities else 'None'}  \n\n"

        # Add summary statistics
        summary = result.get("summary", {})
        header += f"**Results Summary**:  \n"
        header += f"- Selected Entities: {result.get('context_bundle', {}).get('total_entities', 0)}  \n"
        header += f"- Token Utilization: {summary.get('token_utilization', 'N/A')}  \n"
        header += f"- Critical Entities: {summary.get('critical_entities', 0)}  \n"
        header += f"- Files Involved: {summary.get('files_involved', 0)}  \n\n"

        # Add selection rationale
        rationale = result.get("context_bundle", {}).get("selection_rationale", "")
        if rationale:
            header += f"**Selection Rationale**: {rationale}  \n\n"

        # Format the IR slices and code context
        display_content = header

        # Add IR slices if available
        if result.get("ir_slices"):
            display_content += "## IR Analysis Slices\n\n```json\n"
            ir_slices_preview = result["ir_slices"][:3]  # Show first 3 for preview
            display_content += json.dumps(ir_slices_preview, indent=2)
            if len(result["ir_slices"]) > 3:
                display_content += f"\n... and {len(result['ir_slices']) - 3} more entities"
            display_content += "\n```\n\n"

        # Add code context if available
        if result.get("code_context"):
            display_content += "## Code Context\n\n"
            for i, code_ctx in enumerate(result["code_context"][:3]):  # Show first 3
                display_content += f"### {code_ctx['entity_name']} ({code_ctx['file_path']})\n"
                display_content += f"**Priority**: {code_ctx['priority']} | **Relevance**: {code_ctx['relevance_score']:.2f}\n\n"
                display_content += "```python\n" + code_ctx["source_code"][:500] + "\n```\n\n"

            if len(result["code_context"]) > 3:
                display_content += f"*... and {len(result['code_context']) - 3} more code contexts*\n\n"

        display_content += "*Complete IR context and code sent to LLM for analysis*\n"

        # Store in reflected_message to get the same formatting as other requests
        if hasattr(self, 'reflected_message') and self.reflected_message:
            self.reflected_message = display_content + "\n\n---\n\n" + self.reflected_message
        else:
            self.reflected_message = display_content

```

### 59. _get_from_cache
- **File**: context_request_handler.py
- **Priority**: critical
- **Relevance Score**: 2.4985897435897435

```python
    def _get_from_cache(self, cache_key: str) -> Any:
        """Get a value from the cache if it exists and is not expired."""
        if cache_key in self.cache:
            timestamp = self.cache_timestamps.get(cache_key, 0)
            if (timestamp + self.cache_ttl) > time.time():
                return self.cache[cache_key]
        return None

```

### 60. check_environment_setup
- **File**: debug_context_request_path.py
- **Priority**: critical
- **Relevance Score**: 2.4985897435897435

```python
def check_environment_setup():
    """Check if we're in the right environment for the trading project."""
    print("\n🌍 Environment Setup Check")
    print("=" * 60)
    
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")
    
    # Check if this looks like a trading project
    trading_indicators = [
        'trade_management',
        'services',
        'position_observer.py',
        'position_exit_manager.py',
        'trading',
        'backtest',
        'dashboard'
    ]
    
    found_indicators = []
    for root, dirs, files in os.walk(current_dir):
        for indicator in trading_indicators:
            if indicator in dirs or indicator in files:
                found_indicators.append(indicator)
                rel_path = os.path.relpath(root, current_dir)
                print(f"  📍 Found '{indicator}' in {rel_path}")
    
    if found_indicators:
        print(f"✅ Found {len(found_indicators)} trading project indicators")
    else:
        print(f"❌ No trading project indicators found")
        print(f"💡 This suggests we're in the wrong directory!")
        print(f"💡 The context request system needs to run in your trading project directory")
    
    return len(found_indicators) > 0

```

### 61. print_formatted_results
- **File**: demo_intelligent_code_discovery.py
- **Priority**: critical
- **Relevance Score**: 2.4985897435897435

```python
def print_formatted_results(results):
    """Print results in the exact format shown in the documentation."""
    
    summary = results.get('analysis_summary', {})
    critical = results.get('critical_entities', [])
    safe = results.get('safe_entities', [])
    related = results.get('related_entities', [])
    guidance = results.get('implementation_guidance', '')
    recommendations = results.get('recommendations', [])
    dependency_map = results.get('dependency_map', {})
    
    print()
    print("🎯 INTELLIGENT CODE DISCOVERY RESULTS")
    print("=" * 50)
    print()
    
    print("📊 Analysis Summary:")
    print(f"- Analyzed {summary.get('total_entities_analyzed', 0)} entities across multiple modules")
    print(f"- Selected {summary.get('entities_selected', 0)} relevant entities")
    print(f"- Analysis confidence: {summary.get('selection_confidence', 0.0):.2f}/1.0")
    print(f"- Task: {summary.get('task_description', 'N/A')}")
    print()
    
    print("🔴 CRITICAL ENTITIES (Handle with Care):")
    if critical:
        for i, entity in enumerate(critical[:3], 1):  # Show top 3
            print(f"  📁 {entity['file_path']}")
            print(f"  🔧 {entity['entity_name']} ({entity['entity_type']}) - {entity['criticality'].title()} criticality")
            print(f"  ⚠️  Risk: {entity['risk_explanation']}")
            print()
    else:
        print("  No critical entities identified")
        print()
    
    print("🟢 SAFE INTEGRATION POINTS (Start Here):")
    if safe:
        for i, entity in enumerate(safe[:3], 1):  # Show top 3
            print(f"  📁 {entity['file_path']}")
            print(f"  🔧 {entity['entity_name']} ({entity['entity_type']})")
            print(f"  ✅ Why safe: {entity['why_safe']}")
            print(f"  💡 Suggestion: {entity['integration_suggestion']}")
            print()
    else:
        print("  No safe integration points identified")
        print()
    
    print("🟡 RELATED CODE (May Need Modification):")
    if related:
        for i, entity in enumerate(related[:3], 1):  # Show top 3
            print(f"  📁 {entity['file_path']}")
            print(f"  🔧 {entity['entity_name']} ({entity['entity_type']}) - Relevance: {entity['relevance_score']:.2f}")
            print(f"  🔗 Relationship: {entity['relationship_type']}")
            print()
    else:
        print("  No related entities identified")
        print()
    
    print("📋 IMPLEMENTATION GUIDANCE:")
    for line in guidance.split('\n'):
        if line.strip():
            print(f"  {line.strip()}")
    print()
    
    print("🔗 DEPENDENCY MAP (Top Dependencies):")
    if dependency_map:
        count = 0
        for entity_name, deps in dependency_map.items():
            if count >= 3:  # Show top 3
                break
            if deps['calls'] or deps['used_by']:
                print(f"  {entity_name}:")
                if deps['calls']:
                    print(f"    Calls: {', '.join(deps['calls'][:3])}")
                if deps['used_by']:
                    print(f"    Used by: {', '.join(deps['used_by'][:3])}")
                count += 1
        print()
    else:
        print("  No significant dependencies identified")
        print()
    
    print("💡 RECOMMENDATIONS:")
    for i, rec in enumerate(recommendations[:5], 1):  # Show top 5
        print(f"  {i}. {rec}")
    print()


```

### 62. tool_output
- **File**: test_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.4985897435897435

```python
    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")

```

### 63. test_repo_messages_structure
- **File**: test_context_request_priority_fix.py
- **Priority**: critical
- **Relevance Score**: 2.4985897435897435

```python
def test_repo_messages_structure():
    """Test that get_repo_messages() no longer contains conflicting instructions."""

    try:
        from aider.coders.base_coder import Coder
        from aider.models import Model
        from aider.io import InputOutput
        from aider.repomap import RepoMap

        # Create a minimal coder instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()

        # Create a coder instance
        coder = Coder.create(
            main_model=model,
            io=io,
            fnames=[],
            use_git=False,
            map_tokens=1000
        )

        # Get the repo messages
        repo_messages = coder.get_repo_messages()

        print("=== REPO MESSAGES STRUCTURE ===")
        print(f"Number of messages: {len(repo_messages)}")

        for i, msg in enumerate(repo_messages):
            print(f"\nMessage {i+1} ({msg['role']}):")
            content = msg['content']
            if len(content) > 200:
                print(f"  Content: {content[:200]}...")
            else:
                print(f"  Content: {content}")

        # If there are no repo messages, that might be because there's no repo map
        if len(repo_messages) == 0:
            print("\n⚠️  No repo messages found - this might be because there's no repository map available")
            print("   This is expected in a test environment without a proper git repository")
            return True

        # Check that the assistant response is now minimal and doesn't contain conflicting instructions
        if len(repo_messages) >= 2:
            assistant_msg = repo_messages[1]
            if assistant_msg['role'] == 'assistant':
                content = assistant_msg['content']

                # The new assistant response should be short and focused
                if len(content) < 200 and "CONTEXT_REQUEST" in content and "primary method" in content:
                    print("\n✅ SUCCESS: Assistant response is now concise and emphasizes CONTEXT_REQUEST")
                    return True
                else:
                    print(f"\n❌ ISSUE: Assistant response is still too detailed or doesn't emphasize CONTEXT_REQUEST properly")
                    print(f"Content: {content}")
                    return False

        print("\n❌ ISSUE: Expected structure not found")
        return False

    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 64. print_discovery_results
- **File**: test_intelligent_code_discovery.py
- **Priority**: critical
- **Relevance Score**: 2.4985897435897435

```python
def print_discovery_results(results: Dict[str, Any]):
    """Print the discovery results in a user-friendly format."""
    
    # Analysis Summary
    summary = results.get('analysis_summary', {})
    print(f"\n📊 Analysis Summary:")
    print(f"   Total entities analyzed: {summary.get('total_entities_analyzed', 0)}")
    print(f"   Entities selected: {summary.get('entities_selected', 0)}")
    print(f"   Selection confidence: {summary.get('selection_confidence', 0.0):.2f}")
    print(f"   Task: {summary.get('task_description', 'N/A')}")
    
    # Critical Entities
    critical = results.get('critical_entities', [])
    print(f"\n🔴 Critical Entities ({len(critical)}):")
    for i, entity in enumerate(critical[:3], 1):  # Show top 3
        print(f"   {i}. {entity['entity_name']} ({entity['entity_type']})")
        print(f"      File: {entity['file_path']}")
        print(f"      Risk: {entity['risk_explanation']}")
        print(f"      Used by: {entity['used_by_count']} components")
    
    # Safe Entities
    safe = results.get('safe_entities', [])
    print(f"\n🟢 Safe Integration Points ({len(safe)}):")
    for i, entity in enumerate(safe[:3], 1):  # Show top 3
        print(f"   {i}. {entity['entity_name']} ({entity['entity_type']})")
        print(f"      File: {entity['file_path']}")
        print(f"      Why safe: {entity['why_safe']}")
        print(f"      Suggestion: {entity['integration_suggestion']}")
    
    # Related Entities
    related = results.get('related_entities', [])
    print(f"\n🟡 Related Entities ({len(related)}):")
    for i, entity in enumerate(related[:3], 1):  # Show top 3
        print(f"   {i}. {entity['entity_name']} ({entity['entity_type']})")
        print(f"      Relationship: {entity['relationship_type']}")
        print(f"      Relevance: {entity['relevance_score']:.2f}")
    
    # Implementation Guidance
    guidance = results.get('implementation_guidance', '')
    print(f"\n📋 Implementation Guidance:")
    for line in guidance.split('\n'):
        if line.strip():
            print(f"   {line.strip()}")
    
    # Recommendations
    recommendations = results.get('recommendations', [])
    print(f"\n💡 Recommendations:")
    for i, rec in enumerate(recommendations[:5], 1):  # Show top 5
        print(f"   {i}. {rec}")


```

### 65. get_contextual_dependencies
- **File**: aider_integration_service.py
- **Priority**: critical
- **Relevance Score**: 2.4966666666666666

```python
    def get_contextual_dependencies(self, project_path: str, primary_file: str,
                                   max_snippets: int = 20) -> Dict:
        """
        Get a comprehensive map of contextual dependencies for a file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            max_snippets: Maximum number of snippets to include

        Returns:
            A dictionary with contextual dependency information
        """
        extractor = self._get_context_extractor()
        context_map = extractor.get_contextual_dependencies(project_path, primary_file, max_snippets)

        # Convert to a serializable dictionary
        result = {
            'primary_file': context_map.primary_file,
            'related_files': context_map.related_files,
            'usage_contexts': [],
            'definition_contexts': []
        }

        # Add usage contexts
        for usage_ctx in context_map.usage_contexts:
            result['usage_contexts'].append({
                'file_path': usage_ctx.snippet.file_path,
                'start_line': usage_ctx.snippet.start_line,
                'end_line': usage_ctx.snippet.end_line,
                'content': usage_ctx.snippet.content,
                'symbol_name': usage_ctx.snippet.symbol_name,
                'usage_type': usage_ctx.usage_type.value,
                'surrounding_function': usage_ctx.snippet.surrounding_function
            })

        # Add definition contexts
        for def_ctx in context_map.definition_contexts:
            result['definition_contexts'].append({
                'file_path': def_ctx.snippet.file_path,
                'start_line': def_ctx.snippet.start_line,
                'end_line': def_ctx.snippet.end_line,
                'content': def_ctx.snippet.content,
                'symbol_name': def_ctx.snippet.symbol_name,
                'definition_type': def_ctx.definition_type.value,
                'signature': def_ctx.signature,
                'docstring': def_ctx.docstring
            })

        return result

```

### 66. tool_error
- **File**: test_context_request_hang.py
- **Priority**: critical
- **Relevance Score**: 2.4902564102564098

```python
    def tool_error(self, message, **kwargs):
        self.errors.append(message)
        print(f"[ERROR] {message}")


```

### 67. tool_error
- **File**: test_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.4902564102564098

```python
    def tool_error(self, message, **kwargs):
        self.errors.append(message)
        print(f"[ERROR] {message}")


```

### 68. test_context_request_parsing
- **File**: test_context_request_format_fix.py
- **Priority**: critical
- **Relevance Score**: 2.48974358974359

```python
def test_context_request_parsing():
    """Test that the context request parsing handles both correct and incorrect formats."""
    print("\n🧪 Testing CONTEXT_REQUEST Parsing")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler
        
        handler = ContextRequestHandler("aider-main")
        
        print("✅ ContextRequestHandler created successfully")
        
        # Test 1: Correct format (should work)
        print("\n🔍 Testing correct CONTEXT_REQUEST format...")
        
        correct_request = '''
        I need to understand the function implementation.
        
        {CONTEXT_REQUEST: {
          "original_user_query_context": "how does the close_position_based_on_conditions function work?",
          "symbols_of_interest": [
            {"type": "function_definition", "name": "close_position_based_on_conditions", "file_hint": "trade_management/position_exit_manager.py"}
          ],
          "reason_for_request": "To analyze the implementation of the function"
        }}
        '''
        
        parsed_correct = handler.parse_context_request(correct_request)
        
        if parsed_correct:
            print("✅ Correct format parsed successfully")
            print(f"   - Symbols count: {len(parsed_correct.symbols_of_interest)}")
            if parsed_correct.symbols_of_interest:
                symbol = parsed_correct.symbols_of_interest[0]
                print(f"   - First symbol: type='{symbol.type}', name='{symbol.name}', file_hint='{symbol.file_hint}'")
        else:
            print("❌ Correct format failed to parse")
            return False
        
        # Test 2: Incorrect format (should fail or be handled gracefully)
        print("\n🔍 Testing incorrect CONTEXT_REQUEST format (string array)...")
        
        incorrect_request = '''
        I need to understand the function implementation.
        
        {CONTEXT_REQUEST: {
          "original_user_query_context": "how does the close_position_based_on_conditions function work?",
          "symbols_of_interest": ["close_position_based_on_conditions", "PositionCloser", "position", "conditions"],
          "reason_for_request": "To analyze the implementation"
        }}
        '''
        
        parsed_incorrect = handler.parse_context_request(incorrect_request)
        
        if parsed_incorrect:
            print("⚠️  Incorrect format was parsed (system tried to handle it)")
            print(f"   - Symbols count: {len(parsed_incorrect.symbols_of_interest)}")
            if parsed_incorrect.symbols_of_interest:
                symbol = parsed_incorrect.symbols_of_interest[0]
                print(f"   - First symbol: type='{symbol.type}', name='{symbol.name}', file_hint='{symbol.file_hint}'")
        else:
            print("✅ Incorrect format correctly rejected")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing CONTEXT_REQUEST parsing: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 69. test_context_request_object_access
- **File**: test_context_request_object_fix.py
- **Priority**: critical
- **Relevance Score**: 2.48974358974359

```python
def test_context_request_object_access():
    """Test that ContextRequest objects are accessed correctly."""
    print("🧪 Testing ContextRequest Object Access Fix")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequest, SymbolRequest

        # Create a ContextRequest object
        context_request = ContextRequest(
            original_user_query_context="Testing object access",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="test_function",
                    file_hint="test.py"
                )
            ],
            reason_for_request="Testing attribute access"
        )

        print("🔍 Testing ContextRequest object attribute access...")

        # Test direct attribute access (this should work)
        success_criteria = []

        try:
            original_context = context_request.original_user_query_context
            success_criteria.append((True, f"✅ original_user_query_context: '{original_context}'"))
        except Exception as e:
            success_criteria.append((False, f"❌ original_user_query_context failed: {e}"))

        try:
            reason = context_request.reason_for_request
            success_criteria.append((True, f"✅ reason_for_request: '{reason}'"))
        except Exception as e:
            success_criteria.append((False, f"❌ reason_for_request failed: {e}"))

        try:
            symbols = context_request.symbols_of_interest
            success_criteria.append((True, f"✅ symbols_of_interest: {len(symbols)} symbols"))
        except Exception as e:
            success_criteria.append((False, f"❌ symbols_of_interest failed: {e}"))

        # Test that .get() method doesn't exist (this should fail)
        try:
            context_request.get('original_user_query_context', '')
            success_criteria.append((False, "❌ .get() method should not exist on ContextRequest"))
        except AttributeError:
            success_criteria.append((True, "✅ .get() method correctly doesn't exist"))
        except Exception as e:
            success_criteria.append((False, f"❌ Unexpected error testing .get(): {e}"))

        # Test hasattr checks (used in our fix)
        try:
            has_symbols = hasattr(context_request, 'symbols_of_interest')
            has_reason = hasattr(context_request, 'reason_for_request')
            has_context = hasattr(context_request, 'original_user_query_context')
            success_criteria.append((has_symbols and has_reason and has_context, 
                                   f"✅ hasattr checks work: symbols={has_symbols}, reason={has_reason}, context={has_context}"))
        except Exception as e:
            success_criteria.append((False, f"❌ hasattr checks failed: {e}"))

        # Print results
        passed = 0
        total = len(success_criteria)

        for condition, message in success_criteria:
            print(f"  {message}")
            if condition:
                passed += 1

        print(f"\n📊 ContextRequest object access: {passed}/{total} tests passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing ContextRequest object access: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 70. test_partial_context_request
- **File**: test_partial_context_request.py
- **Priority**: critical
- **Relevance Score**: 2.48974358974359

```python
def test_partial_context_request():
    """Test that context requests handle partial failures gracefully."""
    print("🧪 Testing Partial Context Request Handling")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, ContextRequest, SymbolRequest

        handler = ContextRequestHandler(".")

        # Create a context request with one valid and one invalid symbol
        context_request = ContextRequest(
            original_user_query_context="Testing partial failure handling",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="trade_management/position_exit_manager.py"  # This might exist
                ),
                SymbolRequest(
                    type="method_definition", 
                    name="close_position_based_on_conditions",
                    file_hint="services/position_observer.py"  # This might not exist or not contain the symbol
                ),
                SymbolRequest(
                    type="method_definition",
                    name="nonexistent_function",
                    file_hint="nonexistent/file.py"  # This definitely doesn't exist
                )
            ],
            reason_for_request="Testing partial failure handling"
        )

        print("🔍 Testing context request with mixed valid/invalid symbols...")
        print(f"Symbols requested:")
        for symbol in context_request.symbols_of_interest:
            print(f"  - {symbol.name} in {symbol.file_hint}")

        # Process the context request
        result = handler.process_context_request(context_request)

        print(f"\n📊 Results:")
        print(f"  Extracted symbols: {len(result.get('extracted_symbols', []))}")
        print(f"  Not found symbols: {len(result.get('not_found_symbols', []))}")
        print(f"  Dependency snippets: {len(result.get('dependency_snippets', []))}")

        # Check that we got partial results
        extracted_symbols = result.get('extracted_symbols', [])
        not_found_symbols = result.get('not_found_symbols', [])

        success_criteria = [
            (len(extracted_symbols) > 0, "At least one symbol was extracted"),
            (len(not_found_symbols) > 0, "Missing symbols were tracked"),
            (len(extracted_symbols) + len(not_found_symbols) == len(context_request.symbols_of_interest), "All symbols accounted for"),
        ]

        passed = 0
        total = len(success_criteria)

        for condition, description in success_criteria:
            if condition:
                print(f"  ✅ {description}")
                passed += 1
            else:
                print(f"  ❌ {description}")

        # Show details of what was found and what wasn't
        if extracted_symbols:
            print(f"\n✅ Successfully extracted symbols:")
            for symbol in extracted_symbols:
                symbol_name = symbol.get('symbol_name', '')
                file_path = symbol.get('file_path', '')
                content_length = len(symbol.get('content', ''))
                print(f"  - {symbol_name} from {file_path} ({content_length} chars)")

        if not_found_symbols:
            print(f"\n⚠️  Symbols not found (gracefully handled):")
            for symbol in not_found_symbols:
                symbol_name = symbol.get('symbol_name', '')
                reason = symbol.get('reason', '')
                print(f"  - {symbol_name}: {reason}")

        print(f"\n📊 Partial context request: {passed}/{total} criteria passed")
        return passed == total

    except Exception as e:
        print(f"❌ Error testing partial context request: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 71. process_context_request
- **File**: test_augmented_prompt_content.py
- **Priority**: critical
- **Relevance Score**: 2.487820512820513

```python
    def process_context_request(self, request):
        """
        Process a context request, extracting the requested symbols and their dependencies.
        """
        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
            "dependency_snippets": []
        }

        # Process each requested symbol
        for symbol in request.symbols_of_interest:
            # Extract the symbol name (handle class.method format)
            symbol_parts = symbol.name.split('.')
            if len(symbol_parts) > 1:
                # This is a class.method format
                class_name = symbol_parts[0]
                method_name = symbol_parts[1]

                # Find the file containing the symbol
                file_path = self.find_file_defining_symbol("", symbol.name)
                if not file_path:
                    continue

                # Extract the method content
                content = self.extract_symbol_content(method_name, file_path, "")
                if not content:
                    continue

                # Extract essential imports
                essential_imports = self.extract_essential_imports("", file_path)

                # Extract containing class
                containing_class = self.extract_containing_class("", file_path, method_name)

                # Extract usage contexts
                usage_contexts = self.extract_usage_contexts("", method_name, file_path)

                # Add the extracted symbol to the result
                result["extracted_symbols"].append({
                    "symbol_name": symbol.name,
                    "file_path": file_path,
                    "content": content,
                    "essential_imports": essential_imports,
                    "containing_class": containing_class
                })

                # Add dependency snippets
                for usage in usage_contexts:
                    result["dependency_snippets"].append({
                        "file_path": usage.get("file_path", ""),
                        "symbol_name": usage.get("symbol_name", ""),
                        "content": usage.get("content", ""),
                        "usage_type": usage.get("usage_type", "unknown")
                    })

        return result

```

### 72. get_related_entities
- **File**: intelligent_context_selector.py
- **Priority**: critical
- **Relevance Score**: 2.4855128205128207

```python
    def get_related_entities(self, entity_name: str, max_depth: int = 2) -> List[str]:
        """Get entities related to the given entity through dependencies."""
        related = set()
        visited = set()

```

### 73. debug_tree_context
- **File**: debug_repo_map.py
- **Priority**: critical
- **Relevance Score**: 2.482820512820513

```python
def debug_tree_context():
    """Debug TreeContext to see if it's doing the slicing."""
    print("\n🔍 Debugging TreeContext")
    print("=" * 60)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create a repo map instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=1000,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        # Test render_tree directly
        print("🧪 Testing render_tree directly:")
        
        abs_fname = "aider-main/aider/coders/base_coder.py"
        rel_fname = "aider/coders/base_coder.py"
        lois = [1, 10, 20, 30]  # Some lines of interest
        
        tree_result = repo_map.render_tree(abs_fname, rel_fname, lois)
        
        if tree_result:
            print(f"   Tree result length: {len(tree_result)} characters")
            print(f"   Contains '⋮': {'⋮' in tree_result}")
            print(f"   First 200 chars: {tree_result[:200]}...")
            
            if '⋮' in tree_result:
                print("   ❌ SLICING DETECTED in render_tree!")
                # Find where the ⋮ symbols are
                lines = tree_result.split('\n')
                for i, line in enumerate(lines[:10]):
                    if '⋮' in line:
                        print(f"   Line {i}: {line}")
            else:
                print("   ✅ No slicing detected in render_tree")
        else:
            print("   Tree result is None")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging TreeContext: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 74. test_binary_search_algorithm_removal
- **File**: test_complete_repo_map_elimination.py
- **Priority**: critical
- **Relevance Score**: 2.482820512820513

```python
def test_binary_search_algorithm_removal():
    """Test that the binary search slicing algorithm is not executed."""
    print("\n🧪 Testing Binary Search Algorithm Removal")
    print("=" * 60)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create a repo map instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=1000,  # Small token limit to trigger slicing in old system
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        print("✅ RepoMap instance created with small token limit")
        
        # Try to trigger the binary search algorithm
        result = repo_map.get_ranked_tags_map(
            chat_fnames=[],
            other_fnames=["aider-main/aider/coders/base_coder.py", "aider-main/aider/repomap.py"],
            max_map_tokens=500,  # Very small limit
            mentioned_fnames=set(),
            mentioned_idents=set(),
            force_refresh=True
        )
        
        if result is None:
            print("✅ get_ranked_tags_map() returns None - binary search not executed")
            return True
        else:
            print("❌ get_ranked_tags_map() returned content - binary search may have executed")
            print(f"   Content length: {len(result)} characters")
            print(f"   Content preview: {result[:200]}...")
            
            # Check for slicing indicators
            if '⋮' in result:
                print("   ❌ Contains slicing indicator '⋮' - binary search executed!")
                return False
            else:
                print("   ✅ No slicing indicators found")
                return True
        
    except Exception as e:
        print(f"❌ Error testing binary search algorithm: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 75. test_context_request_new_format
- **File**: test_directory_file_format.py
- **Priority**: critical
- **Relevance Score**: 2.482820512820513

```python
def test_context_request_new_format():
    """Test that CONTEXT_REQUEST handles the new directory_name/file_name format."""
    print("🧪 Testing CONTEXT_REQUEST New Format")
    print("=" * 60)

    try:
        from aider.context_request.context_request_handler import ContextRequestHandler

        handler = ContextRequestHandler(".")

        # Test the new format
        new_format_request = '''
        {CONTEXT_REQUEST: {"original_user_query_context": "How does the function work?", "symbols_of_interest": [{"type": "method_definition", "name": "close_position_based_on_conditions", "directory_name": "trade_management", "file_name": "position_exit_manager.py"}], "reason_for_request": "analyze the implementation"}}
        '''

        print("🔍 Testing new directory_name/file_name format...")
        print(f"Request: {new_format_request.strip()}")

        context_request = handler.parse_context_request(new_format_request)

        if context_request:
            print("✅ Successfully parsed CONTEXT_REQUEST with new format!")
            print(f"   Original query context: {context_request.original_user_query_context}")
            print(f"   Number of symbols: {len(context_request.symbols_of_interest)}")
            
            if context_request.symbols_of_interest:
                symbol = context_request.symbols_of_interest[0]
                print(f"   Symbol name: {symbol.name}")
                print(f"   Symbol type: {symbol.type}")
                print(f"   File hint (constructed): {symbol.file_hint}")
                
                # Check that file_hint was constructed correctly
                expected_path = "trade_management/position_exit_manager.py"
                if symbol.file_hint == expected_path:
                    print(f"✅ File hint correctly constructed: {symbol.file_hint}")
                    return True
                else:
                    print(f"❌ File hint incorrect. Expected: {expected_path}, Got: {symbol.file_hint}")
                    return False
            else:
                print("❌ No symbols found in parsed request")
                return False
        else:
            print("❌ Failed to parse CONTEXT_REQUEST")
            return False

    except Exception as e:
        print(f"❌ Error testing CONTEXT_REQUEST new format: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 76. test_context_request_error_handling
- **File**: test_error_handling_fix.py
- **Priority**: critical
- **Relevance Score**: 2.482820512820513

```python
def test_context_request_error_handling():
    """Test that CONTEXT_REQUEST errors are properly communicated to the LLM."""
    print("🔍 Testing CONTEXT_REQUEST Error Handling")
    print("=" * 80)

    try:
        from aider.context_request import ContextRequestHandler, ContextRequest, SymbolRequest, AiderTemplateRenderer

        print("✅ Context request modules imported successfully")

        # Create a context request handler
        handler = ContextRequestHandler(project_path="aider-main")
        renderer = AiderTemplateRenderer()

        print("✅ Handler and renderer created successfully")

        # Test 1: Symbol not found scenario
        print("\n🧪 Test 1: Symbol not found scenario")
        print("-" * 60)

        # Create a context request for a non-existent symbol
        context_request = ContextRequest(
            original_user_query_context="how does the close_position_based_on_conditions function work?",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="close_position_based_on_conditions",
                    file_hint="position_observer.py"
                )
            ],
            reason_for_request="To retrieve the definition of the 'close_position_based_on_conditions' method"
        )

        # Process the context request
        result = handler.process_context_request(context_request)

        print(f"   Extracted symbols: {len(result.get('extracted_symbols', []))}")
        print(f"   Not found symbols: {len(result.get('not_found_symbols', []))}")

        # Check if not_found_symbols are properly tracked
        not_found_symbols = result.get('not_found_symbols', [])
        if not_found_symbols:
            print("   ✅ Not found symbols properly tracked:")
            for symbol in not_found_symbols:
                symbol_name = symbol.get('symbol_name', '')
                reason = symbol.get('reason', '')
                print(f"     - {symbol_name}: {reason}")
        else:
            print("   ❌ Not found symbols not tracked")
            return False

        # Test 2: Template renderer formats errors properly
        print("\n🧪 Test 2: Template renderer error formatting")
        print("-" * 60)

        # Generate the augmented prompt
        augmented_prompt = renderer.render_augmented_prompt(
            original_query="how does the close_position_based_on_conditions function work?",
            repo_overview="Test repository overview",
            extracted_context=result
        )

        # Check if the error is included in the prompt
        if "⚠️ SYMBOLS NOT FOUND ⚠️" in augmented_prompt:
            print("   ✅ Error section properly included in prompt")

            # Check if suggestions are included
            if "SUGGESTIONS:" in augmented_prompt:
                print("   ✅ Helpful suggestions included")
            else:
                print("   ❌ Suggestions missing")
                return False

            # Check if the specific symbol is mentioned
            if "close_position_based_on_conditions" in augmented_prompt:
                print("   ✅ Specific symbol mentioned in error")
            else:
                print("   ❌ Specific symbol not mentioned")
                return False

        else:
            print("   ❌ Error section not included in prompt")
            print("   Prompt preview:")
            print(augmented_prompt[:500] + "...")
            return False

        # Test 3: Check that LLM gets actionable guidance
        print("\n🧪 Test 3: Actionable guidance for LLM")
        print("-" * 60)

        guidance_keywords = [
            "REQUEST_FILE",
            "MAP_REQUEST",
            "spelled correctly",
            "different file",
            "alternative approaches"
        ]

        guidance_found = 0
        for keyword in guidance_keywords:
            if keyword in augmented_prompt:
                guidance_found += 1
                print(f"   ✅ Found guidance: '{keyword}'")
            else:
                print(f"   ❌ Missing guidance: '{keyword}'")

        if guidance_found >= 3:
            print(f"   ✅ Sufficient actionable guidance provided ({guidance_found}/{len(guidance_keywords)})")
        else:
            print(f"   ❌ Insufficient actionable guidance ({guidance_found}/{len(guidance_keywords)})")
            return False

        return True

    except Exception as e:
        print(f"❌ Error in context request error handling test: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 77. test_integration_with_context_request
- **File**: test_repomap_style_file_discovery.py
- **Priority**: critical
- **Relevance Score**: 2.482820512820513

```python
def test_integration_with_context_request():
    """Test the integration with actual CONTEXT_REQUEST processing."""
    print("\n🧪 Testing Integration with CONTEXT_REQUEST")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, SymbolRequest
        from aider.context_request.aider_integration_service import AiderIntegrationService
        
        # Create a context request handler
        project_path = "aider-main"
        aider_service = AiderIntegrationService()
        handler = ContextRequestHandler(project_path, aider_service)
        
        # Test symbol request with file hint
        symbol = SymbolRequest(
            type="method_definition",
            name="get_repo_map",
            file_hint="aider/coders/base_coder.py"
        )
        
        print(f"🎯 Testing symbol: {symbol.name}")
        print(f"   File hint: {symbol.file_hint}")
        
        # Test the _find_file_for_symbol method
        found_file = handler._find_file_for_symbol(symbol)
        
        if found_file:
            print(f"   ✅ Found file: {found_file}")
            
            # Verify the file exists
            full_path = os.path.join(project_path, found_file) if not os.path.isabs(found_file) else found_file
            if os.path.exists(full_path):
                print(f"   ✅ File exists on disk")
                
                # Try to extract symbol content
                file_path, symbol_name, content = handler._extract_symbol_content(symbol)
                if content:
                    print(f"   ✅ Successfully extracted symbol content")
                    print(f"   📝 Content length: {len(content)} characters")
                    print(f"   📝 Content preview: {content[:100]}...")
                    return True
                else:
                    print(f"   ❌ Failed to extract symbol content")
                    return False
            else:
                print(f"   ❌ File doesn't exist: {full_path}")
                return False
        else:
            print(f"   ❌ Failed to find file for symbol")
            return False
            
    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 78. test_no_slicing_algorithm_execution
- **File**: test_repository_map_slicing_termination.py
- **Priority**: critical
- **Relevance Score**: 2.482820512820513

```python
def test_no_slicing_algorithm_execution():
    """Test that the slicing algorithm in get_ranked_tags_map_uncached is not executed."""
    print("\n🧪 Testing No Slicing Algorithm Execution")
    print("=" * 60)
    
    try:
        from aider.repomap import RepoMap
        from aider.models import Model
        from aider.io import InputOutput
        
        # Create a repo map instance
        model = Model("gpt-3.5-turbo")
        io = InputOutput()
        
        repo_map = RepoMap(
            map_tokens=1000,
            root="aider-main",
            main_model=model,
            io=io,
            verbose=False
        )
        
        print("✅ RepoMap instance created successfully")
        
        # Test if get_repo_map returns None (should when Smart Map Request is available)
        result = repo_map.get_repo_map(
            chat_files=set(),
            other_files=["aider-main/aider/coders/base_coder.py"],
            mentioned_fnames=set(),
            mentioned_idents=set()
        )
        
        if result is None:
            print("✅ RepoMap.get_repo_map() returns None (no slicing)")
            return True
        else:
            print("❌ RepoMap.get_repo_map() returned content (slicing occurred)!")
            print(f"   Content length: {len(result)} characters")
            print(f"   Content preview: {result[:200]}...")
            
            # Check for slicing indicators
            if "⋮" in result:
                print("   ❌ Contains slicing indicator '⋮'")
            if "..." in result:
                print("   ❌ Contains truncation indicator '...'")
            
            return False
        
    except Exception as e:
        print(f"❌ Error testing slicing algorithm: {e}")
        import traceback
        traceback.print_exc()
        return False

```

### 79. test_context_request_integration
- **File**: test_simplified_system.py
- **Priority**: critical
- **Relevance Score**: 2.482820512820513

```python
def test_context_request_integration():
    """Test that Context Request System still works."""
    print("\n🧪 Testing Context Request Integration")
    print("=" * 60)

    try:
        # Add the aider-main directory to the path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

        from aider.coders.base_coder import CONTEXT_REQUEST_AVAILABLE

        if CONTEXT_REQUEST_AVAILABLE:
            print("✅ Context Request System is available")

            from aider.context_request import ContextRequestHandler
            print("✅ ContextRequestHandler can be imported")

            return True
        else:
            print("❌ Context Request System not available")
            return False

    except Exception as e:
        print(f"❌ Context Request integration test failed: {e}")
        return False

```

### 80. main
- **File**: context_request_demo.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
def main():
    """
    Demo script for the context request functionality.
    """
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the integration service
    aider_service = AiderIntegrationService()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path, aider_service)
    
    # Print the LLM instructions
    print("\n=== LLM Instructions ===")
    print(integration.get_llm_instructions())
    
    # Create a sample context request
    context_request = ContextRequest(
        original_user_query_context="User is asking about the surgical file extractor",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name="SurgicalFileExtractor.extract_symbol_content",
                file_hint="surgical_file_extractor.py"
            ),
            SymbolRequest(
                type="class_definition",
                name="SurgicalFileExtractor",
                file_hint="surgical_file_extractor.py"
            )
        ],
        reason_for_request="To understand how the surgical file extractor works"
    )
    
    # Process the context request
    print("\n=== Processing Context Request ===")
    print(f"Request: {integration.get_context_request_summary(context_request)}")
    
    # Sample repository overview
    repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
surgical_context_extractor.py:
│class SurgicalContextExtractor:
│    def extract_usage_contexts(self, project_path, symbol_name, defining_file):
│    def extract_dependency_contexts(self, project_path, primary_file):
│    def extract_definition_contexts(self, project_path, symbols, source_file):
"""
    
    # Generate the augmented prompt
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the surgical file extractor work?",
        repo_overview=repo_overview
    )
    
    # Print the augmented prompt
    print("\n=== Augmented Prompt ===")
    print(augmented_prompt)
    
    # Test with a real LLM response
    llm_response = """
I need to understand how the surgical file extractor works to answer your question properly.

{CONTEXT_REQUEST: { 
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "SurgicalFileExtractor.extract_symbol_content", "file_hint": "surgical_file_extractor.py"},
    {"type": "class_definition", "name": "SurgicalFileExtractor", "file_hint": "surgical_file_extractor.py"}
  ],
  "reason_for_request": "To understand how the surgical file extractor works"
}}
"""
    
    # Detect the context request
    print("\n=== Detecting Context Request ===")
    detected_request = integration.detect_context_request(llm_response)
    if detected_request:
        print(f"Detected request: {integration.get_context_request_summary(detected_request)}")
    else:
        print("No context request detected")


if __name__ == "__main__":
    main()
```

### 81. get_contextual_dependencies
- **File**: surgical_context_extractor.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
    def get_contextual_dependencies(self, project_path: str, primary_file: str,
                                   max_snippets: int = 20) -> ContextualDependencyMap:
        """
        Get a comprehensive map of contextual dependencies for a file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            max_snippets: Maximum number of snippets to include

        Returns:
            A ContextualDependencyMap object
        """
        # Extract dependency contexts
        dependency_contexts = self.extract_dependency_contexts(project_path, primary_file)

        # Get all symbols defined in the primary file
        symbols_in_primary = self.aider_service.get_symbols_defined_in_file(project_path, primary_file)

        # Flatten the symbols list
        all_symbols = []
        for symbol_type, symbols in symbols_in_primary.items():
            all_symbols.extend(symbols)

        # Get usage contexts for symbols defined in the primary file
        usage_contexts = []
        for symbol in all_symbols:
            symbol_usages = self.extract_usage_contexts(project_path, symbol, primary_file)
            usage_contexts.extend(symbol_usages)

        # Get definition contexts for symbols used in the primary file
        definition_contexts = []

        # Get files imported by the primary file
        imported_files = self.aider_service.get_files_imported_by(project_path, primary_file)

        # For each imported file, get symbols referenced from the primary file
        for imported_file in imported_files:
            symbol_refs = self.aider_service.get_symbol_references_between_files(
                project_path, primary_file, imported_file)

            # Flatten the symbols list
            referenced_symbols = []
            for symbol_type, symbols in symbol_refs.items():
                referenced_symbols.extend(symbols)

            # Get definition contexts for these symbols
            if referenced_symbols:
                symbol_definitions = self.extract_definition_contexts(
                    project_path, referenced_symbols, primary_file)
                definition_contexts.extend(symbol_definitions)

        # Sort contexts by relevance and limit to max_snippets
        usage_contexts.sort(key=lambda x: x.snippet.relevance_score, reverse=True)
        definition_contexts.sort(key=lambda x: x.snippet.relevance_score, reverse=True)

        # Ensure we have a balanced mix of usage and definition contexts
        max_each = max_snippets // 2
        usage_contexts = usage_contexts[:max_each]
        definition_contexts = definition_contexts[:max_each]

        # Get related files
        related_files = list(set(
            [ctx.snippet.file_path for ctx in usage_contexts] +
            [ctx.snippet.file_path for ctx in definition_contexts]
        ))

        return ContextualDependencyMap(
            primary_file=primary_file,
            usage_contexts=usage_contexts,
            definition_contexts=definition_contexts,
            related_files=related_files
        )
```

### 82. main
- **File**: test_aider_context_request.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
def main():
    print("\n=== Testing CONTEXT_REQUEST in Aider Session ===")

    # Get the project path
    project_path = os.getcwd()

    # Initialize the mock coder
    mock_coder = MockCoder(project_path)

    # Create a sample context request
    context_request_content = """
    I need more information about the process_context_request method.

    {CONTEXT_REQUEST:
      "symbols_of_interest": [
        {
          "type": "method_definition",
          "name": "process_context_request",
          "file_hint": "base_coder.py"
        }
      ],
      "reason_for_request": "To analyze the implementation of the process_context_request method"
    }
    """

    # Process the context request
    print("\nProcessing context request...")
    cleaned_content, augmented_prompt = mock_coder.process_context_requests(
        content=context_request_content,
        user_message="How does the process_context_request method work?"
    )

    # Print the augmented prompt
    if augmented_prompt:
        print("\n=== Augmented Prompt Content ===")
        print(augmented_prompt)

        # Extract the code context from the augmented prompt
        code_context_start = augmented_prompt.find("### REQUESTED SYMBOL DEFINITIONS")
        code_context_end = augmented_prompt.find("INSTRUCTIONS FOR THIS TURN")

        if code_context_start != -1 and code_context_end != -1:
            code_context = augmented_prompt[code_context_start:code_context_end].strip()
            print("\n=== Code Context ===")
            print(code_context)

            # Check if the process_context_request method is in the code context
            if "process_context_request" in code_context:
                print("\n✅ process_context_request method found in code context")

                # Check if the method implementation is complete
                if "def process_context_request" in code_context and "return augmented_prompt" in code_context:
                    print("✅ Complete method implementation found")
                else:
                    print("❌ Method implementation is incomplete")
            else:
                print("\n❌ process_context_request method not found in code context")
        else:
            print("\n❌ No code context found in the augmented prompt")
    else:
        print("\n❌ No augmented prompt generated")

    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
```

### 83. tool_output
- **File**: test_aider_context_request.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
    def tool_output(self, message="", **kwargs):
        self.outputs.append(message)
        print(f"[TOOL] {message}")

```

### 84. main
- **File**: test_context_request.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
def main():
    args = parse_args()
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the integration service
    aider_service = AiderIntegrationService()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path, aider_service)
    
    # Print the LLM instructions
    print("\n=== LLM Instructions ===")
    print(integration.get_llm_instructions())
    
    # Create a sample context request
    context_request = ContextRequest(
        original_user_query_context="User is asking about the surgical file extractor",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name=args.symbol,
                file_hint=args.file_hint
            )
        ],
        reason_for_request=args.reason
    )
    
    # Process the context request
    print("\n=== Processing Context Request ===")
    print(f"Request: {integration.get_context_request_summary(context_request)}")
    
    # Sample repository overview
    repo_overview = """
surgical_file_extractor.py:
│class SurgicalFileExtractor:
│    def extract_symbol_content(self, target_symbol, file_path, project_path):
│    def extract_symbol_range(self, target_symbol, file_path, project_path):
│    def get_symbols_in_file(self, project_path, file_path):
surgical_context_extractor.py:
│class SurgicalContextExtractor:
│    def extract_usage_contexts(self, project_path, symbol_name, defining_file):
│    def extract_dependency_contexts(self, project_path, primary_file):
│    def extract_definition_contexts(self, project_path, symbols, source_file):
"""
    
    # Generate the augmented prompt
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the surgical file extractor work?",
        repo_overview=repo_overview
    )
    
    # Print the augmented prompt
    print("\n=== Augmented Prompt ===")
    print(augmented_prompt)
    
    # Test with a real LLM response
    llm_response = f"""
I need to understand how the surgical file extractor works to answer your question properly.

{{CONTEXT_REQUEST: {{ 
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {{"type": "method_definition", "name": "{args.symbol}", "file_hint": "{args.file_hint}"}}
  ],
  "reason_for_request": "{args.reason}"
}}}}
"""
    
    # Detect the context request
    print("\n=== Detecting Context Request ===")
    detected_request = integration.detect_context_request(llm_response)
    if detected_request:
        print(f"Detected request: {integration.get_context_request_summary(detected_request)}")
        
        # Process the detected request
        print("\n=== Processing Detected Context Request ===")
        augmented_prompt = integration.process_context_request(
            context_request=detected_request,
            original_user_query="How does the surgical file extractor work?",
            repo_overview=repo_overview
        )
        
        # Print the augmented prompt
        print("\n=== Augmented Prompt from Detected Request ===")
        print(augmented_prompt)
    else:
        print("No context request detected")


if __name__ == "__main__":
    main()
```

### 85. main
- **File**: test_context_request_availability.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
def main():
    print("\n=== Testing CONTEXT_REQUEST Availability ===")
    
    if not CONTEXT_REQUEST_AVAILABLE:
        print("❌ CONTEXT_REQUEST functionality is not available")
        return
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the context request integration
    try:
        integration = AiderContextRequestIntegration(project_path)
        print("✅ Successfully initialized AiderContextRequestIntegration")
    except Exception as e:
        print(f"❌ Failed to initialize AiderContextRequestIntegration: {e}")
        return
    
    # Get the LLM instructions
    try:
        instructions = integration.get_llm_instructions()
        print("✅ Successfully got LLM instructions")
        print("\nLLM Instructions:")
        print(instructions)
    except Exception as e:
        print(f"❌ Failed to get LLM instructions: {e}")
    
    # Create a sample context request
    try:
        context_request = ContextRequest(
            original_user_query_context="User is asking about the surgical file extractor",
            symbols_of_interest=[
                SymbolRequest(
                    type="method_definition",
                    name="SurgicalFileExtractor.extract_symbol_content",
                    file_hint="surgical_file_extractor.py"
                )
            ],
            reason_for_request="To understand how the surgical file extractor works"
        )
        print("✅ Successfully created ContextRequest")
    except Exception as e:
        print(f"❌ Failed to create ContextRequest: {e}")
        return
    
    # Get the context request summary
    try:
        summary = integration.get_context_request_summary(context_request)
        print("✅ Successfully got context request summary")
        print(f"Summary: {summary}")
    except Exception as e:
        print(f"❌ Failed to get context request summary: {e}")
    
    print("\n=== Test completed! ===")

if __name__ == "__main__":
    main()
```

### 86. main
- **File**: test_context_request_code_block_fix.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
def main():
    """Run all tests to verify the CONTEXT_REQUEST code block fix."""
    
    print("CONTEXT_REQUEST Code Block Closure Fix Verification")
    print("=" * 60)
    
    test1_passed = test_context_request_code_block_closure()
    test2_passed = test_markdown_stream_handling()
    test3_passed = test_code_block_flow_simulation()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"  Context Request Processing: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"  Markdown Stream Handling: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"  Code Block Flow Simulation: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    
    total_passed = sum([test1_passed, test2_passed, test3_passed])
    
    if total_passed == 3:
        print(f"\n🎉 ALL {total_passed}/3 TESTS PASSED!")
        print("\nThe fix should resolve the code block issue where:")
        print("  - CONTEXT_REQUEST blocks are properly closed")
        print("  - Follow-up responses appear outside the code block")
        print("  - Markdown streaming is properly reinitialized")
        return True
    else:
        print(f"\n❌ {total_passed}/3 tests passed - some issues may remain")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

### 87. main
- **File**: test_context_request_integration.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
def main():
    print("\n=== Testing CONTEXT_REQUEST Integration ===")

    # Create a mock coder
    coder = MockCoder()

    # Simulate a user message
    user_message = "How does the surgical file extractor work?"
    print(f"\nUser: {user_message}")

    # Simulate an LLM response with a context request
    llm_response = """
I need to understand how the surgical file extractor works to answer your question properly.

{CONTEXT_REQUEST: {
  "original_user_query_context": "User is asking about the surgical file extractor",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "SurgicalFileExtractor.extract_symbol_content", "file_hint": "surgical_file_extractor.py"}
  ],
  "reason_for_request": "To understand how the surgical file extractor works"
}}
"""
    print(f"\nLLM: {llm_response}")

    # Process the context request
    print("\nProcessing context request...")
    start_time = time.time()
    cleaned_content, context_prompt = coder.process_context_requests(llm_response, user_message)

    # Check if a context request was detected
    if context_prompt:
        print("✅ Context request detected and processed!")
        print("\nAugmented prompt:")
        print(context_prompt[:200] + "..." if len(context_prompt) > 200 else context_prompt)

        # Create a messages list for testing
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": user_message}
        ]

        # Simulate sending the augmented prompt to the LLM
        print("\nSending augmented prompt to LLM...")

        # Use the send method to test if it hangs
        response_chunks = []
        for chunk in coder.send(messages):
            response_chunks.append(chunk)
            print(f"Received chunk: {chunk}")

        end_time = time.time()

        # Check if the process completed in a reasonable time
        if end_time - start_time < 5:  # Less than 5 seconds
            print("✅ Process completed successfully without hanging!")
        else:
            print("❌ Process took too long, might be hanging.")
    else:
        print("❌ Context request was not detected or processed")

    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
```

### 88. main
- **File**: test_context_request_real.py
- **Priority**: critical
- **Relevance Score**: 2.4735897435897436

```python
def main():
    print("\n=== Testing CONTEXT_REQUEST with Real Code ===")
    
    # Get the project path
    project_path = os.getcwd()
    
    # Initialize the context request integration
    integration = AiderContextRequestIntegration(project_path)
    
    # Create a sample context request for a real function in the codebase
    context_request = ContextRequest(
        original_user_query_context="User is asking about the position quantity calculator",
        symbols_of_interest=[
            SymbolRequest(
                type="method_definition",
                name="AiderContextRequestIntegration.process_context_request",
                file_hint="aider_context_request_integration.py"
            )
        ],
        reason_for_request="To analyze the implementation of the process_context_request method"
    )
    
    # Process the context request
    print("\nProcessing context request...")
    augmented_prompt = integration.process_context_request(
        context_request=context_request,
        original_user_query="How does the process_context_request method work?",
        repo_overview=""
    )
    
    # Print the augmented prompt
    print("\n=== Augmented Prompt Content ===")
    print(augmented_prompt)
    
    # Extract the code context from the augmented prompt
    code_context_start = augmented_prompt.find("### REQUESTED SYMBOL DEFINITIONS")
    code_context_end = augmented_prompt.find("INSTRUCTIONS FOR THIS TURN")
    
    if code_context_start != -1 and code_context_end != -1:
        code_context = augmented_prompt[code_context_start:code_context_end].strip()
        print("\n=== Code Context ===")
        print(code_context)
    else:
        print("\n❌ No code context found in the augmented prompt")
    
    print("\n=== Test completed! ===")


if __name__ == "__main__":
    main()
```

## INSTRUCTIONS FOR LLM
You have been provided with intelligent context selection based on IR (Intermediate Representation) analysis and ICD (Intelligent Code Discovery).

### Context Quality
- This context was selected using task-specific algorithms
- Entities are prioritized by criticality and relevance
- Dependencies and risk factors have been analyzed
- Token budget has been optimized for maximum value

### Your Task
Please analyze the provided context and respond to the user's query: "How does the intelligent context selection algorithm work? I need to understand the implementation."

Use the IR analysis data to understand:
1. **Entity Criticality**: Focus on high-criticality components
2. **Change Risk**: Consider risk factors when making recommendations
3. **Dependencies**: Understand how components interact
4. **Side Effects**: Be aware of potential impacts
5. **Error Patterns**: Identify potential issues

Provide a comprehensive, accurate response based on this intelligent context selection.
