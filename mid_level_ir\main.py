"""
Main pipeline orchestrator for Mid-Level IR generation.

This module coordinates the entire analysis pipeline, managing the flow of data
through each analysis module and providing the main entry point for IR generation.
"""

import time
from pathlib import Path
from typing import Dict, List, Optional, Any

from .ir_context import IRContext, ModuleInfo
from .file_scanner import FileScanner
from .entity_extractor import EntityExtractor
from .call_graph_builder import CallGraphBuilder
from .dependency_analyzer import DependencyAnalyzer
from .inheritance_analyzer import InheritanceAnalyzer
from .side_effect_analyzer import SideEffectAnalyzer
from .error_analyzer import ErrorAnalyzer
from .metadata_enricher import MetadataEnricher
from .criticality_scorer import CriticalityScorer
from .ir_builder import IRBuilder


class MidLevelIRPipeline:
    """
    Main pipeline for generating Mid-Level Intermediate Representation.
    
    This class orchestrates the entire analysis process, coordinating multiple
    specialized analyzers to produce comprehensive codebase analysis.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the pipeline with optional configuration.
        
        Args:
            config: Configuration dictionary for customizing analysis behavior
        """
        self.config = config or {}
        
        # Initialize pipeline modules
        self.file_scanner = FileScanner(self.config.get('file_scanner', {}))
        self.entity_extractor = EntityExtractor(self.config.get('entity_extractor', {}))
        self.call_graph_builder = CallGraphBuilder(self.config.get('call_graph_builder', {}))
        self.dependency_analyzer = DependencyAnalyzer(self.config.get('dependency_analyzer', {}))
        self.inheritance_analyzer = InheritanceAnalyzer(self.config.get('inheritance_analyzer', {}).get('verbose', False))
        self.side_effect_analyzer = SideEffectAnalyzer(self.config.get('side_effect_analyzer', {}))
        self.error_analyzer = ErrorAnalyzer(self.config.get('error_analyzer', {}))
        self.metadata_enricher = MetadataEnricher(self.config.get('metadata_enricher', {}))
        self.criticality_scorer = CriticalityScorer(self.config.get('criticality_scorer', {}))
        self.ir_builder = IRBuilder(self.config.get('ir_builder', {}))
    
    def generate_ir(self, project_path: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate complete Mid-Level IR for a project.
        
        Args:
            project_path: Path to the project root directory
            output_path: Optional path to save the generated IR JSON
            
        Returns:
            Dictionary containing the complete IR structure
        """
        start_time = time.time()
        project_path = Path(project_path)
        
        print(f"🚀 Starting Mid-Level IR generation for: {project_path}")
        print("=" * 60)
        
        # Initialize context
        context = IRContext(
            project_path=project_path,
            project_name=project_path.name,
            config=self.config
        )
        
        try:
            # Phase 1: File Discovery and Parsing
            print("📁 Phase 1: File Discovery and Parsing")
            context = self.file_scanner.scan(context)
            print(f"   Found {len(context.all_files)} Python files")
            
            # Phase 2: Entity Extraction
            print("🔍 Phase 2: Entity Extraction")
            context = self.entity_extractor.extract(context)
            stats = context.get_statistics()
            print(f"   Extracted {stats['total_entities']} entities from {stats['total_modules']} modules")
            
            # Phase 3: Call Graph Building
            print("🔗 Phase 3: Call Graph Analysis")
            context = self.call_graph_builder.build(context)
            print(f"   Built call graph with {len(context.global_call_graph)} nodes")
            
            # Phase 4: Dependency Analysis
            print("📦 Phase 4: Dependency Analysis")
            context = self.dependency_analyzer.analyze(context)
            stats = context.get_statistics()
            print(f"   Analyzed {stats['total_dependencies']} dependencies")

            # Phase 4.5: Inheritance Analysis (NEW)
            print("🧬 Phase 4.5: Inheritance Analysis")
            context = self.inheritance_analyzer.analyze(context)
            stats = context.get_statistics()
            print(f"   Analyzed {stats.get('total_class_hierarchies', 0)} class hierarchies")

            # Phase 5: Side Effect Analysis
            print("⚡ Phase 5: Side Effect Analysis")
            context = self.side_effect_analyzer.analyze(context)
            
            # Phase 6: Error Analysis
            print("🚨 Phase 6: Error Analysis")
            context = self.error_analyzer.analyze(context)
            
            # Phase 7: Metadata Enrichment
            print("📊 Phase 7: Metadata Enrichment")
            context = self.metadata_enricher.enrich(context)
            
            # Phase 8: Criticality Scoring
            print("🎯 Phase 8: Criticality Scoring")
            context = self.criticality_scorer.score(context)
            
            # Phase 9: IR Building
            print("🏗️ Phase 9: IR Assembly")
            ir_data = self.ir_builder.build(context)
            
            # Add generation metadata
            generation_time = time.time() - start_time
            ir_data["metadata"]["generation_time_seconds"] = generation_time
            ir_data["metadata"]["pipeline_version"] = "2.0.0"
            
            print(f"✅ IR generation completed in {generation_time:.2f} seconds")
            
            # Save to file if requested
            if output_path:
                self.ir_builder.save_to_file(ir_data, output_path)
                print(f"💾 IR saved to: {output_path}")
            
            # Print final statistics
            self._print_final_statistics(ir_data)
            
            return ir_data
            
        except Exception as e:
            print(f"❌ Error during IR generation: {e}")
            raise
    
    def _print_final_statistics(self, ir_data: Dict[str, Any]) -> None:
        """Print comprehensive statistics about the generated IR."""
        metadata = ir_data.get("metadata", {})
        modules = ir_data.get("modules", [])
        
        print(f"\n📈 Final IR Statistics:")
        print("=" * 40)
        print(f"   Total modules: {len(modules)}")
        print(f"   Total entities: {metadata.get('total_entities', 0)}")
        print(f"   Total functions: {metadata.get('total_functions', 0)}")
        print(f"   Total classes: {metadata.get('total_classes', 0)}")
        print(f"   Total methods: {metadata.get('total_methods', 0)}")
        print(f"   Total dependencies: {metadata.get('total_dependencies', 0)}")
        print(f"   Total class hierarchies: {metadata.get('total_class_hierarchies', 0)}")
        print(f"   Total LOC: {metadata.get('total_loc', 0):,}")
        print(f"   Generation time: {metadata.get('generation_time_seconds', 0):.2f}s")
        
        # Show criticality distribution
        if modules:
            criticality_counts = {"high": 0, "medium": 0, "low": 0}
            for module in modules:
                for entity in module.get("entities", []):
                    crit = entity.get("criticality", "low")
                    criticality_counts[crit] = criticality_counts.get(crit, 0) + 1
            
            print(f"\n   📊 Criticality distribution:")
            print(f"     High: {criticality_counts['high']}")
            print(f"     Medium: {criticality_counts['medium']}")
            print(f"     Low: {criticality_counts['low']}")


def main():
    """Command-line entry point for the pipeline."""
    import argparse
    import json
    
    parser = argparse.ArgumentParser(description="Generate Mid-Level IR for Python projects")
    parser.add_argument("project_path", help="Path to the project directory")
    parser.add_argument("-o", "--output", help="Output JSON file path")
    parser.add_argument("-c", "--config", help="Configuration JSON file path")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    
    args = parser.parse_args()
    
    # Load configuration if provided
    config = {}
    if args.config:
        with open(args.config, 'r') as f:
            config = json.load(f)
    
    if args.verbose:
        config['verbose'] = True
    
    # Create and run pipeline
    pipeline = MidLevelIRPipeline(config)
    
    output_path = args.output or f"{Path(args.project_path).name}_ir.json"
    ir_data = pipeline.generate_ir(args.project_path, output_path)
    
    print(f"\n🎉 Mid-Level IR generation completed successfully!")
    print(f"   Output: {output_path}")


if __name__ == "__main__":
    main()
