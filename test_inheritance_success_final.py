#!/usr/bin/env python3
"""
Final test to demonstrate inheritance analysis success by targeting specific inheritance entities.
"""

import os
import sys
import json
import time

def test_inheritance_success_final():
    """Test inheritance analysis by targeting specific inheritance entities."""
    
    print("🎯 Final Inheritance Success Test")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)
        
        # Load the enhanced IR data
        print("📂 Loading enhanced IR data...")
        
        if not os.path.exists("final_enhanced_ir.json"):
            print("❌ Enhanced IR file not found! Run test_final_real_inheritance.py first.")
            return False
        
        with open("final_enhanced_ir.json", 'r') as f:
            ir_data = json.load(f)
        
        print("✅ Enhanced IR data loaded")
        
        # Create context request with specific inheritance entities
        print("\n🎯 Creating targeted inheritance context request...")
        
        from aider.context_request import Con<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRContextRequest
        
        # Create handler and force it to use our enhanced IR
        project_path = "aider-main/aider/coders"
        handler = ContextRequestHandler(project_path)
        handler._ir_cache = ir_data  # Force use of our enhanced IR
        
        # Target specific inheritance entities that we know exist
        enhanced_request = IRContextRequest(
            user_query="Show me the UnknownEditFormat class and its __init__ method inheritance patterns",
            task_description="Analyze the UnknownEditFormat class inheritance from ValueError and its __init__ method super() call",
            task_type="inheritance_analysis",
            focus_entities=[
                "UnknownEditFormat",  # Class that inherits from ValueError
                "__init__",           # Method that calls super()
                "MissingAPIKeyError", # Another class that inherits from ValueError
                "FinishReasonLength", # Class that inherits from Exception
                "Coder"               # Class with methods
            ],
            max_tokens=3000,
            include_ir_slices=True,
            include_code_context=True,
            llm_friendly=True,
            max_output_chars=25000,
            max_entities=15  # Increase to capture more entities
        )
        
        # Process the request
        print("\n⚡ Processing targeted inheritance request...")
        
        start_time = time.time()
        result = handler.process_ir_context_request(enhanced_request)
        processing_time = time.time() - start_time
        
        print(f"✅ Request processed in {processing_time:.2f}s")
        
        # Analyze the result
        print("\n📋 Analyzing targeted inheritance package...")
        
        if "llm_friendly_package" in result:
            package_content = result["llm_friendly_package"]
            package_size = result["package_size_chars"]
            compatibility = result["llm_compatibility"]
            
            print(f"📦 Package generated successfully!")
            print(f"   Size: {package_size:,} characters")
            print(f"   Compatibility: {compatibility}")
            
            # Check for specific inheritance content
            inheritance_indicators = [
                "UnknownEditFormat",
                "ValueError",
                "Belongs to Class:",
                "Inherits From:",
                "calls_super",
                "super()",
                "MissingAPIKeyError",
                "FinishReasonLength",
                "Exception",
                "(method)",
                "class_name",
                "🔁 Inheritance Context",
                "Access: protected"
            ]
            
            found_indicators = []
            for indicator in inheritance_indicators:
                if indicator in package_content:
                    found_indicators.append(indicator)
            
            print(f"\n🔍 Inheritance Content Analysis:")
            print(f"   Found {len(found_indicators)}/{len(inheritance_indicators)} inheritance indicators")
            
            for indicator in found_indicators:
                print(f"   ✅ {indicator}")
            
            missing_indicators = [ind for ind in inheritance_indicators if ind not in found_indicators]
            for indicator in missing_indicators:
                print(f"   ❌ {indicator} (not found)")
            
            # Save the targeted inheritance package
            output_file = "targeted_inheritance_package.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(package_content)
            print(f"\n💾 Targeted inheritance package saved to: {output_file}")
            
            # Show inheritance content samples
            print(f"\n📄 Inheritance content samples:")
            lines = package_content.split('\n')
            for i, line in enumerate(lines):
                if any(indicator in line for indicator in ["UnknownEditFormat", "ValueError", "Belongs to Class", "Inherits From", "calls_super"]):
                    # Show context around inheritance lines
                    start = max(0, i-2)
                    end = min(len(lines), i+5)
                    print("```")
                    for j in range(start, end):
                        marker = ">>> " if j == i else "    "
                        print(f"{marker}{lines[j]}")
                    print("```")
                    print("---")
                    break
            
            # Check IR slices for inheritance data
            if "ir_slices" in result:
                print(f"\n🔍 Checking IR slices for inheritance data...")
                inheritance_slices = []
                for ir_slice in result["ir_slices"]:
                    if (ir_slice.get("class_name") or 
                        ir_slice.get("inherits_from") or 
                        ir_slice.get("calls_super") or
                        ir_slice.get("entity_name") in ["UnknownEditFormat", "MissingAPIKeyError", "__init__"]):
                        inheritance_slices.append(ir_slice)
                
                print(f"   Found {len(inheritance_slices)} IR slices with inheritance data:")
                for slice_data in inheritance_slices[:3]:  # Show first 3
                    entity_name = slice_data.get("entity_name", "unknown")
                    entity_type = slice_data.get("entity_type", "unknown")
                    class_name = slice_data.get("class_name", "")
                    inherits_from = slice_data.get("inherits_from", [])
                    calls_super = slice_data.get("calls_super", False)
                    
                    print(f"   • {entity_name} ({entity_type})")
                    if class_name:
                        print(f"     - Class: {class_name}")
                    if inherits_from:
                        print(f"     - Inherits: {inherits_from}")
                    if calls_super:
                        print(f"     - Calls super: {calls_super}")
            
            # Determine success level
            if len(found_indicators) >= 10:
                return "excellent"
            elif len(found_indicators) >= 7:
                return "good"
            elif len(found_indicators) >= 4:
                return "partial"
            else:
                return "minimal"
        else:
            print("❌ LLM-friendly package was not generated!")
            return "failure"
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return "failure"

if __name__ == "__main__":
    print("🧪 Final Inheritance Success Test")
    print("=" * 80)
    
    result = test_inheritance_success_final()
    
    print(f"\n🎯 Final Result: {result}")
    
    if result == "excellent":
        print("🎉 EXCELLENT! Inheritance analysis is working perfectly!")
        print("✅ Check targeted_inheritance_package.txt for complete inheritance analysis")
        print("🚀 Enhanced inheritance features are fully functional!")
    elif result == "good":
        print("✅ GOOD! Most inheritance features are working correctly")
        print("💡 Check targeted_inheritance_package.txt for details")
    elif result == "partial":
        print("⚠️  PARTIAL: Some inheritance features are working")
        print("💡 Check targeted_inheritance_package.txt for what's captured")
    elif result == "minimal":
        print("⚠️  MINIMAL: Basic inheritance detection is working")
        print("💡 More work needed for complete inheritance analysis")
    else:
        print("❌ FAILURE: Inheritance analysis is not working")
        print("💡 Check the error messages above for debugging")
    
    sys.exit(0 if result in ["excellent", "good", "partial"] else 1)
