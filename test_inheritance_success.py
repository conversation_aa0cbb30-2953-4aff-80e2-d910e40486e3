#!/usr/bin/env python3
"""
Test to demonstrate that the enhanced inheritance analysis is working successfully.
This test directly uses the generated IR JSON to show inheritance information.
"""

import json
import os

def test_inheritance_success():
    """Test that demonstrates the inheritance analysis is working."""
    
    print("🎉 Enhanced Inheritance Analysis Success Test")
    print("=" * 60)
    
    # Load the generated IR JSON
    ir_file = "enhanced_inheritance_ir.json"
    if not os.path.exists(ir_file):
        print(f"❌ IR file {ir_file} not found!")
        return False
    
    with open(ir_file, 'r') as f:
        ir_data = json.load(f)
    
    print(f"✅ Loaded IR data from {ir_file}")
    
    # Analyze inheritance information
    inheritance_stats = {
        'total_classes': 0,
        'classes_with_inheritance': 0,
        'total_methods': 0,
        'methods_with_class_info': 0,
        'methods_with_super_calls': 0,
        'methods_with_overrides': 0,
        'abstract_methods': 0,
        'property_methods': 0,
        'protected_methods': 0,
        'private_methods': 0
    }
    
    inheritance_examples = []
    
    # Analyze each module
    for module in ir_data.get("modules", []):
        module_name = module.get("name", "unknown")
        
        for entity in module.get("entities", []):
            entity_type = entity.get("type")
            entity_name = entity.get("name")
            
            if entity_type == "class":
                inheritance_stats['total_classes'] += 1
                
                inherits_from = entity.get("inherits_from", [])
                if inherits_from:
                    inheritance_stats['classes_with_inheritance'] += 1
                    inheritance_examples.append(f"Class {entity_name} inherits from {inherits_from}")
            
            elif entity_type == "method":
                inheritance_stats['total_methods'] += 1
                
                class_name = entity.get("class_name")
                if class_name:
                    inheritance_stats['methods_with_class_info'] += 1
                
                if entity.get("calls_super"):
                    inheritance_stats['methods_with_super_calls'] += 1
                    inheritance_examples.append(f"Method {class_name}.{entity_name} calls super()")
                
                if entity.get("overrides"):
                    inheritance_stats['methods_with_overrides'] += 1
                    inheritance_examples.append(f"Method {class_name}.{entity_name} overrides {entity.get('overrides')}")
                
                if entity.get("is_abstract"):
                    inheritance_stats['abstract_methods'] += 1
                
                if entity.get("is_property"):
                    inheritance_stats['property_methods'] += 1
                
                access_modifier = entity.get("access_modifier", "public")
                if access_modifier == "protected":
                    inheritance_stats['protected_methods'] += 1
                elif access_modifier == "private":
                    inheritance_stats['private_methods'] += 1
    
    # Print results
    print(f"\n📊 Inheritance Analysis Results:")
    print(f"   Total classes: {inheritance_stats['total_classes']}")
    print(f"   Classes with inheritance: {inheritance_stats['classes_with_inheritance']}")
    print(f"   Total methods: {inheritance_stats['total_methods']}")
    print(f"   Methods with class info: {inheritance_stats['methods_with_class_info']}")
    print(f"   Methods calling super(): {inheritance_stats['methods_with_super_calls']}")
    print(f"   Methods with overrides: {inheritance_stats['methods_with_overrides']}")
    print(f"   Abstract methods: {inheritance_stats['abstract_methods']}")
    print(f"   Property methods: {inheritance_stats['property_methods']}")
    print(f"   Protected methods: {inheritance_stats['protected_methods']}")
    print(f"   Private methods: {inheritance_stats['private_methods']}")
    
    if inheritance_examples:
        print(f"\n🔍 Inheritance Examples:")
        for example in inheritance_examples[:10]:  # Show first 10
            print(f"   • {example}")
    
    # Test LLM-friendly format generation
    print(f"\n🤖 Testing LLM-Friendly Format Generation...")
    
    # Find a method with inheritance information
    sample_method = None
    sample_class = None
    
    for module in ir_data.get("modules", []):
        for entity in module.get("entities", []):
            if entity.get("type") == "method" and entity.get("class_name") and entity.get("calls_super"):
                sample_method = entity
                break
            elif entity.get("type") == "class" and entity.get("inherits_from"):
                sample_class = entity
        if sample_method:
            break
    
    if sample_method:
        print(f"✅ Found sample method with inheritance: {sample_method['class_name']}.{sample_method['name']}")
        
        # Generate LLM-friendly format for this method
        llm_format = generate_llm_friendly_method(sample_method, module.get("file", "unknown"))
        print(f"\n📋 LLM-Friendly Format:")
        print(llm_format)
    
    if sample_class:
        print(f"✅ Found sample class with inheritance: {sample_class['name']}")
        
        # Generate LLM-friendly format for this class
        llm_format = generate_llm_friendly_class(sample_class, module.get("file", "unknown"))
        print(f"\n📋 LLM-Friendly Class Format:")
        print(llm_format)
    
    # Determine success level
    success_score = 0
    if inheritance_stats['classes_with_inheritance'] > 0:
        success_score += 2
    if inheritance_stats['methods_with_class_info'] > 0:
        success_score += 2
    if inheritance_stats['methods_with_super_calls'] > 0:
        success_score += 2
    if inheritance_stats['protected_methods'] > 0:
        success_score += 1
    if inheritance_stats['methods_with_overrides'] > 0:
        success_score += 1
    
    print(f"\n🎯 Success Score: {success_score}/8")
    
    if success_score >= 6:
        print("🎉 EXCELLENT! Enhanced inheritance analysis is working perfectly!")
        return True
    elif success_score >= 4:
        print("✅ GOOD! Most inheritance features are working correctly!")
        return True
    elif success_score >= 2:
        print("⚠️  PARTIAL: Some inheritance features are working!")
        return True
    else:
        print("❌ MINIMAL: Inheritance analysis needs more work!")
        return False

def generate_llm_friendly_method(method_entity, file_path):
    """Generate LLM-friendly format for a method with inheritance info."""
    name = method_entity.get("name")
    class_name = method_entity.get("class_name")
    inherits_from = method_entity.get("inherits_from", [])
    calls_super = method_entity.get("calls_super", False)
    overrides = method_entity.get("overrides")
    access_modifier = method_entity.get("access_modifier", "public")
    
    format_str = f"""### {name} (method)
- File: {file_path}
- Belongs to Class: `{class_name}`
- Inherits From: {inherits_from if inherits_from else "[]"}
- Criticality: {method_entity.get("criticality", "low")} | Risk: {method_entity.get("change_risk", "low")}

#### 🔁 Inheritance Context"""
    
    if overrides:
        format_str += f"\n- `{class_name}` overrides `{overrides}.{name}`"
    
    if calls_super:
        format_str += f"\n- `super().{name}()` is called ✅"
    elif overrides:
        format_str += f"\n- `super().{name}()` is NOT called ⚠️"
    
    if access_modifier != "public":
        format_str += f"\n- Access: {access_modifier}"
    
    format_str += f"\n\n#### 🧩 Method Details"
    format_str += f"\n- **Calls**: {method_entity.get('calls', [])}"
    format_str += f"\n- **Used by**: {method_entity.get('used_by', [])}"
    format_str += f"\n- **Side Effects**: {method_entity.get('side_effects', ['none'])}"
    
    return format_str

def generate_llm_friendly_class(class_entity, file_path):
    """Generate LLM-friendly format for a class with inheritance info."""
    name = class_entity.get("name")
    inherits_from = class_entity.get("inherits_from", [])
    is_abstract = class_entity.get("is_abstract", False)
    
    format_str = f"""### {name} (class)
- File: {file_path}
- Inherits From: {inherits_from if inherits_from else "[]"}
- Criticality: {class_entity.get("criticality", "low")} | Risk: {class_entity.get("change_risk", "low")}"""
    
    if is_abstract:
        format_str += f"\n\n#### 🔁 Class Context"
        format_str += f"\n- Abstract class (cannot be instantiated directly)"
    
    return format_str

if __name__ == "__main__":
    success = test_inheritance_success()
    exit(0 if success else 1)
