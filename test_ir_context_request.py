#!/usr/bin/env python3
"""
Test script for the new IR_CONTEXT_REQUEST functionality.

This script demonstrates how the new intelligent context request system works,
replacing the old MAP_REQUEST -> CONTEXT_REQUEST flow with a direct
IR_CONTEXT_REQUEST that leverages the Mid-Level IR and Intelligent Code Discovery.
"""

import os
import sys
import json
from pathlib import Path

def test_ir_context_request():
    """Test the IR_CONTEXT_REQUEST functionality."""
    
    print("🧠 Testing IR_CONTEXT_REQUEST Integration")
    print("=" * 60)
    
    # Add the aider-main directory to the path
    aider_main_path = os.path.join(os.path.dirname(__file__), 'aider-main')
    if aider_main_path not in sys.path:
        sys.path.insert(0, aider_main_path)
    
    try:
        # Import the required modules
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        print("✅ Successfully imported IR context request modules")
        
        # Create a context request handler
        project_path = os.getcwd()
        handler = ContextRequestHandler(project_path)
        
        print(f"✅ Created ContextRequestHandler for project: {project_path}")
        
        # Test 1: Basic IR context request
        print("\n🔍 Test 1: Basic IR Context Request")
        
        ir_request = IRContextRequest(
            user_query="How does the intelligent context selection work?",
            task_description="Understand the intelligent context selection engine implementation",
            task_type="general_analysis",
            focus_entities=["context", "selection", "intelligent"],
            max_tokens=4000,
            include_ir_slices=True,
            include_code_context=True
        )
        
        print(f"   Task: {ir_request.task_description}")
        print(f"   Type: {ir_request.task_type}")
        print(f"   Focus: {ir_request.focus_entities}")
        
        # Process the IR context request
        result = handler.process_ir_context_request(ir_request)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        print("✅ IR Context Request processed successfully!")
        
        # Display results
        context_bundle = result.get("context_bundle", {})
        summary = result.get("summary", {})
        
        print(f"\n📊 Results Summary:")
        print(f"   Selected Entities: {context_bundle.get('total_entities', 0)}")
        print(f"   Token Utilization: {summary.get('token_utilization', 'N/A')}")
        print(f"   Critical Entities: {summary.get('critical_entities', 0)}")
        print(f"   Files Involved: {summary.get('files_involved', 0)}")
        
        # Show selection rationale
        rationale = context_bundle.get("selection_rationale", "")
        if rationale:
            print(f"\n🎯 Selection Rationale:")
            print(f"   {rationale}")
        
        # Show IR slices preview
        ir_slices = result.get("ir_slices", [])
        if ir_slices:
            print(f"\n📋 IR Slices Preview ({len(ir_slices)} total):")
            for i, slice_data in enumerate(ir_slices[:3]):
                print(f"   {i+1}. {slice_data['entity_name']} ({slice_data['entity_type']})")
                print(f"      File: {slice_data['file_path']}")
                print(f"      Criticality: {slice_data['criticality']}")
                print(f"      Priority: {slice_data['priority']}")
        
        # Show code context preview
        code_context = result.get("code_context", [])
        if code_context:
            print(f"\n💻 Code Context Preview ({len(code_context)} total):")
            for i, ctx in enumerate(code_context[:2]):
                print(f"   {i+1}. {ctx['entity_name']}")
                print(f"      File: {ctx['file_path']}")
                print(f"      Priority: {ctx['priority']}")
                print(f"      Code length: {len(ctx['source_code'])} characters")
        
        # Test 2: Debugging-focused request
        print("\n🐛 Test 2: Debugging-Focused IR Context Request")
        
        debug_request = IRContextRequest(
            user_query="Find potential bugs in the context selection logic",
            task_description="Debug context selection issues and identify error-prone code",
            task_type="debugging",
            focus_entities=["error", "exception", "bug"],
            max_tokens=6000,
            include_ir_slices=True,
            include_code_context=True
        )
        
        debug_result = handler.process_ir_context_request(debug_request)
        
        if "error" not in debug_result:
            debug_summary = debug_result.get("summary", {})
            print(f"   Debug Context Selected: {debug_summary.get('critical_entities', 0)} critical entities")
            print(f"   Token Utilization: {debug_summary.get('token_utilization', 'N/A')}")
            print("✅ Debugging context request successful")
        else:
            print(f"❌ Debug request error: {debug_result['error']}")
        
        # Test 3: Feature development request
        print("\n🚀 Test 3: Feature Development IR Context Request")
        
        feature_request = IRContextRequest(
            user_query="How to extend the system with new analysis capabilities?",
            task_description="Understand extension points for adding new analysis features",
            task_type="feature_development",
            focus_entities=["analyzer", "extension", "plugin"],
            max_tokens=8000,
            include_ir_slices=True,
            include_code_context=True
        )
        
        feature_result = handler.process_ir_context_request(feature_request)
        
        if "error" not in feature_result:
            feature_summary = feature_result.get("summary", {})
            print(f"   Feature Context Selected: {feature_summary.get('critical_entities', 0)} critical entities")
            print(f"   Token Utilization: {feature_summary.get('token_utilization', 'N/A')}")
            print("✅ Feature development context request successful")
        else:
            print(f"❌ Feature request error: {feature_result['error']}")
        
        print("\n🎉 All IR_CONTEXT_REQUEST tests completed successfully!")
        print("\n📝 Usage Instructions:")
        print("   To use in practice, LLM should send:")
        print('   {IR_CONTEXT_REQUEST: {')
        print('     "user_query": "Your question here",')
        print('     "task_description": "What you want to accomplish",')
        print('     "task_type": "debugging|feature_development|general_analysis",')
        print('     "focus_entities": ["keyword1", "keyword2"],')
        print('     "max_tokens": 8000')
        print('   }}')
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure the aider-main directory exists and contains the required modules")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_format():
    """Test the JSON format for IR_CONTEXT_REQUEST."""
    
    print("\n📋 Testing IR_CONTEXT_REQUEST JSON Format")
    print("-" * 50)
    
    # Example JSON requests
    examples = [
        {
            "name": "General Analysis",
            "request": {
                "user_query": "How does the codebase handle file processing?",
                "task_description": "Understand file processing workflow and components",
                "task_type": "general_analysis",
                "focus_entities": ["file", "process", "workflow"],
                "max_tokens": 6000
            }
        },
        {
            "name": "Debugging Session",
            "request": {
                "user_query": "Why is the context selection slow?",
                "task_description": "Identify performance bottlenecks in context selection",
                "task_type": "debugging",
                "focus_entities": ["performance", "slow", "bottleneck"],
                "max_tokens": 4000,
                "include_ir_slices": True,
                "include_code_context": False  # Only IR data for performance analysis
            }
        },
        {
            "name": "Feature Development",
            "request": {
                "user_query": "How to add a new analysis module?",
                "task_description": "Understand the architecture for adding new analysis capabilities",
                "task_type": "feature_development",
                "focus_entities": ["module", "analysis", "architecture", "extension"],
                "max_tokens": 8000
            }
        }
    ]
    
    for example in examples:
        print(f"\n📄 {example['name']} Example:")
        print("   {IR_CONTEXT_REQUEST: " + json.dumps(example['request'], indent=6) + "}")
    
    print("\n✅ JSON format examples generated successfully")

if __name__ == "__main__":
    print("🧠 IR_CONTEXT_REQUEST Integration Test")
    print("=" * 60)
    
    # Test the functionality
    success = test_ir_context_request()
    
    # Test JSON formats
    test_json_format()
    
    if success:
        print("\n🎉 All tests passed! IR_CONTEXT_REQUEST is ready for use.")
        print("\n🔄 New Workflow:")
        print("   Old: LLM → MAP_REQUEST → Smart Search → CONTEXT_REQUEST → Code")
        print("   New: LLM → IR_CONTEXT_REQUEST → IR+ICD Analysis → Intelligent Context")
        print("\n💡 Benefits:")
        print("   - Single request instead of two-step process")
        print("   - Intelligent entity selection based on task type")
        print("   - Risk-aware context prioritization")
        print("   - Dependency-driven context inclusion")
        print("   - Token budget optimization")
    else:
        print("\n❌ Tests failed. Please check the implementation.")
        sys.exit(1)
