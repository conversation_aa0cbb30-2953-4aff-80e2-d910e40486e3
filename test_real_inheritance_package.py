#!/usr/bin/env python3
"""
Generate a real LLM-friendly package with actual inheritance data.
This test uses the enhanced IR data to show real inheritance information.
"""

import os
import sys
import json
import time

def test_real_inheritance_package():
    """Generate a real LLM-friendly package with inheritance data."""
    
    print("🧬 Generating Real Inheritance LLM Package")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)
        
        # Import the required modules
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        print("✅ Successfully imported enhanced IR context request modules")
        
        # Step 1: Clear any cached IR data to force use of enhanced IR
        print("\n🗑️  Step 1: Clearing cached IR data...")
        cache_files = [
            "ir_cache.json",
            "project_ir_cache.json", 
            "aider__500_ir_cache.json",
            "aider-main_aider_coders_ir_cache.json"
        ]
        
        for cache_file in cache_files:
            if os.path.exists(cache_file):
                os.remove(cache_file)
                print(f"   Removed {cache_file}")
        
        # Step 2: Create handler for the enhanced IR directory
        project_path = "aider-main/aider/coders"  # Use the directory we generated enhanced IR for
        handler = ContextRequestHandler(project_path)
        
        print(f"✅ Created ContextRequestHandler for: {project_path}")
        
        # Step 3: Load the enhanced IR data directly
        print("\n📂 Step 3: Loading enhanced IR data...")
        
        if os.path.exists("enhanced_inheritance_ir.json"):
            with open("enhanced_inheritance_ir.json", 'r') as f:
                enhanced_ir_data = json.load(f)
            
            # Force the handler to use our enhanced IR data
            handler._ir_cache = enhanced_ir_data
            print("✅ Loaded enhanced IR data with inheritance information")
            
            # Show some inheritance stats from the loaded data
            inheritance_count = 0
            method_count = 0
            super_call_count = 0
            
            for module in enhanced_ir_data.get("modules", []):
                for entity in module.get("entities", []):
                    if entity.get("type") == "class" and entity.get("inherits_from"):
                        inheritance_count += 1
                    elif entity.get("type") == "method":
                        method_count += 1
                        if entity.get("calls_super"):
                            super_call_count += 1
            
            print(f"   📊 Enhanced IR contains:")
            print(f"      Classes with inheritance: {inheritance_count}")
            print(f"      Methods: {method_count}")
            print(f"      Methods calling super(): {super_call_count}")
        else:
            print("❌ Enhanced IR file not found! Using regular IR generation...")
        
        # Step 4: Create an inheritance-focused context request
        print("\n🎯 Step 4: Creating inheritance-focused context request...")
        
        enhanced_request = IRContextRequest(
            user_query="Show me class inheritance patterns and method overrides in the coder classes",
            task_description="Analyze inheritance relationships, method overrides, and super() calls in coder classes",
            task_type="inheritance_analysis",
            focus_entities=["UnknownEditFormat", "MissingAPIKeyError", "Coder", "BaseCoder", "__init__"],
            max_tokens=2500,
            include_ir_slices=True,
            include_code_context=True,
            llm_friendly=True,
            max_output_chars=25000,
            max_entities=10
        )
        
        # Step 5: Process the request with enhanced IR data
        print("\n🤖 Step 5: Processing inheritance-focused request...")
        
        start_time = time.time()
        result = handler.process_ir_context_request(enhanced_request)
        processing_time = time.time() - start_time
        
        print(f"✅ Request processed in {processing_time:.2f}s")
        
        # Step 6: Analyze and save the inheritance package
        print("\n📋 Step 6: Analyzing inheritance package content...")
        
        if "llm_friendly_package" in result:
            package_content = result["llm_friendly_package"]
            package_size = result["package_size_chars"]
            compatibility = result["llm_compatibility"]
            
            print(f"📦 Package generated successfully!")
            print(f"   Size: {package_size:,} characters")
            print(f"   Compatibility: {compatibility}")
            
            # Check for real inheritance indicators
            real_inheritance_indicators = [
                "Belongs to Class:",
                "Inherits From:",
                "🔁 Inheritance Context",
                "calls_super: true",
                "super().__init__()",
                "Access: protected",
                "class_name",
                "inherits_from",
                "UnknownEditFormat",
                "ValueError"
            ]
            
            found_indicators = []
            for indicator in real_inheritance_indicators:
                if indicator in package_content:
                    found_indicators.append(indicator)
            
            print(f"\n🔍 Real Inheritance Content Analysis:")
            print(f"   Found {len(found_indicators)}/{len(real_inheritance_indicators)} inheritance indicators")
            
            for indicator in found_indicators:
                print(f"   ✅ {indicator}")
            
            missing_indicators = [ind for ind in real_inheritance_indicators if ind not in found_indicators]
            for indicator in missing_indicators:
                print(f"   ❌ {indicator} (not found)")
            
            # Save the real inheritance package
            output_file = "real_inheritance_package.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(package_content)
            print(f"\n💾 Real inheritance package saved to: {output_file}")
            
            # Show a sample of the inheritance content
            print(f"\n📄 Sample inheritance content:")
            lines = package_content.split('\n')
            inheritance_lines = []
            for i, line in enumerate(lines):
                if any(indicator in line for indicator in ["Belongs to Class:", "Inherits From:", "🔁 Inheritance", "calls_super", "super()"]):
                    # Include context around inheritance lines
                    start = max(0, i-1)
                    end = min(len(lines), i+3)
                    inheritance_lines.extend(lines[start:end])
                    inheritance_lines.append("---")
            
            if inheritance_lines:
                print("```")
                for line in inheritance_lines[:20]:  # Show first 20 lines
                    print(line)
                print("```")
            
            # Determine success level
            if len(found_indicators) >= 8:
                return "excellent"
            elif len(found_indicators) >= 6:
                return "good"
            elif len(found_indicators) >= 4:
                return "partial"
            else:
                return "minimal"
        else:
            print("❌ LLM-friendly package was not generated!")
            return "failure"
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return "failure"

if __name__ == "__main__":
    print("🧪 Real Inheritance Package Generation Test")
    print("=" * 80)
    
    result = test_real_inheritance_package()
    
    print(f"\n🎯 Final Result: {result}")
    
    if result == "excellent":
        print("🎉 EXCELLENT! Real inheritance data is perfectly captured in LLM packages!")
        print("✅ Check real_inheritance_package.txt for the complete inheritance analysis")
    elif result == "good":
        print("✅ GOOD! Most inheritance data is captured in LLM packages")
        print("💡 Check real_inheritance_package.txt for details")
    elif result == "partial":
        print("⚠️  PARTIAL: Some inheritance data is captured")
        print("💡 Check real_inheritance_package.txt for what's working")
    elif result == "minimal":
        print("⚠️  MINIMAL: Basic inheritance detection is working")
        print("💡 More work needed for complete inheritance analysis")
    else:
        print("❌ FAILURE: Real inheritance data is not being captured")
        print("💡 Check the error messages above for debugging")
    
    sys.exit(0 if result in ["excellent", "good", "partial"] else 1)
