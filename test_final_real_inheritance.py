#!/usr/bin/env python3
"""
Final test to generate a real LLM-friendly package with actual inheritance data.
This test forces the system to use the enhanced IR data with inheritance information.
"""

import os
import sys
import json
import time

def test_final_real_inheritance():
    """Generate a real LLM-friendly package with inheritance data."""
    
    print("🎯 Final Real Inheritance Package Test")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)
        
        # Step 1: Clear all caches to force fresh generation
        print("🗑️  Step 1: Clearing all cached data...")
        cache_files = [
            "ir_cache.json",
            "project_ir_cache.json", 
            "aider__500_ir_cache.json",
            "aider-main_aider_coders_ir_cache.json"
        ]
        
        for cache_file in cache_files:
            if os.path.exists(cache_file):
                os.remove(cache_file)
                print(f"   Removed {cache_file}")
        
        # Step 2: Generate fresh enhanced IR
        print("\n🧬 Step 2: Generating fresh enhanced IR with inheritance...")
        
        from mid_level_ir.main import MidLevelIRPipeline
        
        config = {
            'inheritance_analyzer': {'verbose': True},
            'entity_extractor': {'verbose': True},
            'verbose': True
        }
        
        pipeline = MidLevelIRPipeline(config)
        project_path = "aider-main/aider/coders"
        
        start_time = time.time()
        ir_data = pipeline.generate_ir(project_path, "final_enhanced_ir.json")
        generation_time = time.time() - start_time
        
        print(f"✅ Enhanced IR generated in {generation_time:.2f}s")
        
        # Step 3: Verify inheritance data in IR
        print("\n🔍 Step 3: Verifying inheritance data in IR...")
        
        inheritance_count = 0
        method_count = 0
        super_call_count = 0
        
        for module in ir_data.get("modules", []):
            for entity in module.get("entities", []):
                if entity.get("type") == "class" and entity.get("inherits_from"):
                    inheritance_count += 1
                elif entity.get("type") == "method":
                    method_count += 1
                    if entity.get("calls_super"):
                        super_call_count += 1
        
        print(f"   📊 IR contains:")
        print(f"      Classes with inheritance: {inheritance_count}")
        print(f"      Methods: {method_count}")
        print(f"      Methods calling super(): {super_call_count}")
        
        if inheritance_count == 0 and method_count == 0:
            print("❌ No inheritance data found in IR! Something is wrong.")
            return False
        
        # Step 4: Create context request with enhanced IR
        print("\n🤖 Step 4: Creating context request with enhanced IR...")
        
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        # Create handler and force it to use our enhanced IR
        handler = ContextRequestHandler(project_path)
        handler._ir_cache = ir_data  # Force use of our enhanced IR
        
        enhanced_request = IRContextRequest(
            user_query="Show me class inheritance patterns and method overrides",
            task_description="Analyze inheritance relationships, method overrides, and super() calls",
            task_type="inheritance_analysis",
            focus_entities=["UnknownEditFormat", "__init__", "MissingAPIKeyError", "Coder"],
            max_tokens=2000,
            include_ir_slices=True,
            include_code_context=True,
            llm_friendly=True,
            max_output_chars=20000,
            max_entities=8
        )
        
        # Step 5: Process the request
        print("\n⚡ Step 5: Processing inheritance-focused request...")
        
        start_time = time.time()
        result = handler.process_ir_context_request(enhanced_request)
        processing_time = time.time() - start_time
        
        print(f"✅ Request processed in {processing_time:.2f}s")
        
        # Step 6: Analyze the result
        print("\n📋 Step 6: Analyzing inheritance package...")
        
        if "llm_friendly_package" in result:
            package_content = result["llm_friendly_package"]
            package_size = result["package_size_chars"]
            compatibility = result["llm_compatibility"]
            
            print(f"📦 Package generated successfully!")
            print(f"   Size: {package_size:,} characters")
            print(f"   Compatibility: {compatibility}")
            
            # Check for real inheritance indicators
            real_inheritance_indicators = [
                "Belongs to Class:",
                "Inherits From:",
                "🔁 Inheritance Context",
                "super().__init__()",
                "calls_super",
                "Access: protected",
                "UnknownEditFormat",
                "ValueError",
                "(method)",
                "class_name"
            ]
            
            found_indicators = []
            for indicator in real_inheritance_indicators:
                if indicator in package_content:
                    found_indicators.append(indicator)
            
            print(f"\n🔍 Real Inheritance Content Analysis:")
            print(f"   Found {len(found_indicators)}/{len(real_inheritance_indicators)} inheritance indicators")
            
            for indicator in found_indicators:
                print(f"   ✅ {indicator}")
            
            missing_indicators = [ind for ind in real_inheritance_indicators if ind not in found_indicators]
            for indicator in missing_indicators:
                print(f"   ❌ {indicator} (not found)")
            
            # Save the final real inheritance package
            output_file = "final_real_inheritance_package.txt"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(package_content)
            print(f"\n💾 Final inheritance package saved to: {output_file}")
            
            # Show sample inheritance content
            print(f"\n📄 Sample inheritance content:")
            lines = package_content.split('\n')
            inheritance_lines = []
            for i, line in enumerate(lines):
                if any(indicator in line for indicator in ["Belongs to Class:", "Inherits From:", "🔁 Inheritance", "super()", "method)"]):
                    # Include context around inheritance lines
                    start = max(0, i-1)
                    end = min(len(lines), i+4)
                    inheritance_lines.extend(lines[start:end])
                    inheritance_lines.append("---")
                    if len(inheritance_lines) > 30:  # Limit output
                        break
            
            if inheritance_lines:
                print("```")
                for line in inheritance_lines:
                    print(line)
                print("```")
            
            # Determine success level
            if len(found_indicators) >= 8:
                return "excellent"
            elif len(found_indicators) >= 6:
                return "good"
            elif len(found_indicators) >= 4:
                return "partial"
            else:
                return "minimal"
        else:
            print("❌ LLM-friendly package was not generated!")
            return "failure"
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return "failure"

if __name__ == "__main__":
    print("🧪 Final Real Inheritance Package Test")
    print("=" * 80)
    
    result = test_final_real_inheritance()
    
    print(f"\n🎯 Final Result: {result}")
    
    if result == "excellent":
        print("🎉 EXCELLENT! Real inheritance data is perfectly captured!")
        print("✅ Check final_real_inheritance_package.txt for complete inheritance analysis")
        print("🚀 The enhanced inheritance analysis is working perfectly!")
    elif result == "good":
        print("✅ GOOD! Most inheritance data is captured correctly")
        print("💡 Check final_real_inheritance_package.txt for details")
    elif result == "partial":
        print("⚠️  PARTIAL: Some inheritance data is working")
        print("💡 Check final_real_inheritance_package.txt for what's captured")
    elif result == "minimal":
        print("⚠️  MINIMAL: Basic inheritance detection is working")
        print("💡 More work needed for complete inheritance analysis")
    else:
        print("❌ FAILURE: Real inheritance data is not being captured")
        print("💡 Check the error messages above for debugging")
    
    sys.exit(0 if result in ["excellent", "good", "partial"] else 1)
