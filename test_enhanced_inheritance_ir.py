#!/usr/bin/env python3
"""
Test the enhanced IR generation with inheritance analysis.
This tests that the new inheritance analyzer properly captures class hierarchies,
method overrides, and super() calls.
"""

import os
import sys
import time

def test_enhanced_inheritance_ir():
    """Test that the enhanced IR generation captures inheritance information."""
    
    print("🧬 Testing Enhanced IR Generation with Inheritance Analysis")
    print("=" * 70)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)
        
        # Import the required modules
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        print("✅ Successfully imported enhanced IR context request modules")
        
        # Create a context request handler
        project_path = os.getcwd()
        handler = ContextRequestHandler(project_path)
        
        print(f"✅ Created ContextRequestHandler for project: {project_path}")
        
        # Test: Enhanced IR Context Request with inheritance
        print("\n🧬 Test: Enhanced IR Context Request with Inheritance Analysis")
        
        enhanced_request = IRContextRequest(
            user_query="Show me class inheritance and method overrides",
            task_description="Analyze class hierarchies and method inheritance patterns",
            task_type="inheritance_analysis",
            focus_entities=["class", "method", "inheritance", "override"],
            max_tokens=3000,
            include_ir_slices=True,
            include_code_context=True,
            # LLM-friendly options
            llm_friendly=True,
            max_output_chars=35000,
            max_entities=12
        )
        
        start_time = time.time()
        result = handler.process_ir_context_request(enhanced_request)
        processing_time = time.time() - start_time
        
        print(f"✅ Enhanced IR request processed in {processing_time:.2f}s")
        
        # Check that inheritance information was captured
        if "llm_friendly_package" in result:
            package_content = result["llm_friendly_package"]
            package_size = result["package_size_chars"]
            compatibility = result["llm_compatibility"]
            
            print(f"✅ Enhanced LLM-friendly package generated!")
            print(f"   📦 Package size: {package_size:,} characters")
            print(f"   🤖 Compatibility: {compatibility}")
            
            # Check for inheritance-specific content
            inheritance_indicators = [
                "Belongs to Class:",
                "Inherits From:",
                "🔁 Inheritance Context",
                "overrides",
                "super()",
                "🧩 Method Details",
                "calls_super",
                "overridden_by"
            ]
            
            found_indicators = []
            for indicator in inheritance_indicators:
                if indicator in package_content:
                    found_indicators.append(indicator)
            
            print(f"\n🔍 Inheritance Analysis Results:")
            print(f"   Found {len(found_indicators)}/{len(inheritance_indicators)} inheritance indicators")
            
            for indicator in found_indicators:
                print(f"   ✅ {indicator}")
            
            missing_indicators = [ind for ind in inheritance_indicators if ind not in found_indicators]
            for indicator in missing_indicators:
                print(f"   ❌ {indicator} (not found)")
            
            # Save the enhanced package to see the inheritance information
            with open("test_enhanced_inheritance_package.txt", "w", encoding="utf-8") as f:
                f.write(package_content)
            print(f"\n💾 Enhanced package saved to: test_enhanced_inheritance_package.txt")
            
            # Check if we have actual inheritance data vs. placeholders
            if "inheritance data not available" in package_content:
                print(f"   ⚠️  Package contains placeholder inheritance data")
                print(f"   💡 This means the IR generation needs to be regenerated with enhanced pipeline")
                return "partial_success"
            elif len(found_indicators) >= 6:
                print(f"   ✅ Package contains rich inheritance information!")
                return "full_success"
            else:
                print(f"   ⚠️  Package contains some inheritance information but may be incomplete")
                return "partial_success"
                
        else:
            print("❌ Enhanced LLM-friendly package was not generated!")
            return "failure"
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure the aider.context_request module is available")
        return "failure"
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return "failure"

def test_ir_regeneration():
    """Test regenerating IR with the enhanced pipeline."""
    print("\n🔄 Testing IR Regeneration with Enhanced Pipeline")
    print("=" * 60)
    
    try:
        # Import the enhanced IR pipeline
        from mid_level_ir.main import MidLevelIRPipeline
        
        print("✅ Successfully imported enhanced IR pipeline")
        
        # Create pipeline with inheritance analysis enabled
        config = {
            'inheritance_analyzer': {'verbose': True},
            'verbose': True
        }
        
        pipeline = MidLevelIRPipeline(config)
        
        # Generate enhanced IR for a small subset (to save time)
        project_path = "aider-main/aider/coders"  # Focus on coder classes
        
        print(f"🚀 Generating enhanced IR for: {project_path}")
        
        start_time = time.time()
        ir_data = pipeline.generate_ir(project_path)
        generation_time = time.time() - start_time
        
        print(f"✅ Enhanced IR generated in {generation_time:.2f}s")
        
        # Check for inheritance information in the IR
        inheritance_found = False
        method_count = 0
        class_count = 0
        inheritance_examples = []
        
        for module in ir_data.get("modules", []):
            for entity in module.get("entities", []):
                if entity.get("type") == "class":
                    class_count += 1
                    if entity.get("inherits_from"):
                        inheritance_found = True
                        inheritance_examples.append(f"{entity['name']} inherits from {entity['inherits_from']}")
                
                elif entity.get("type") == "method":
                    method_count += 1
                    if entity.get("class_name"):
                        inheritance_found = True
                        if entity.get("overrides"):
                            inheritance_examples.append(f"{entity['class_name']}.{entity['name']} overrides {entity['overrides']}")
                        if entity.get("calls_super"):
                            inheritance_examples.append(f"{entity['class_name']}.{entity['name']} calls super()")
        
        print(f"\n📊 Enhanced IR Analysis Results:")
        print(f"   Classes found: {class_count}")
        print(f"   Methods found: {method_count}")
        print(f"   Inheritance data: {'✅ Found' if inheritance_found else '❌ Not found'}")
        
        if inheritance_examples:
            print(f"\n🔍 Inheritance Examples (first 5):")
            for example in inheritance_examples[:5]:
                print(f"   • {example}")
        
        return "success" if inheritance_found else "no_inheritance_data"
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Enhanced IR pipeline may not be available")
        return "failure"
    except Exception as e:
        print(f"❌ IR regeneration failed: {e}")
        import traceback
        traceback.print_exc()
        return "failure"

if __name__ == "__main__":
    print("🧪 Enhanced Inheritance IR Testing Suite")
    print("=" * 80)
    
    # Test 1: Check current inheritance support
    result1 = test_enhanced_inheritance_ir()
    
    # Test 2: Try regenerating IR with enhanced pipeline
    result2 = test_ir_regeneration()
    
    print(f"\n🎯 Final Results:")
    print(f"   Enhanced IR Context Test: {result1}")
    print(f"   IR Regeneration Test: {result2}")
    
    if result1 == "full_success" and result2 == "success":
        print(f"\n🎉 All tests passed! Enhanced inheritance analysis is working perfectly!")
        sys.exit(0)
    elif result1 in ["partial_success", "full_success"] or result2 == "success":
        print(f"\n✅ Partial success! Enhanced inheritance analysis is partially working.")
        print(f"💡 Check test_enhanced_inheritance_package.txt for detailed output")
        sys.exit(0)
    else:
        print(f"\n❌ Tests failed. Enhanced inheritance analysis needs more work.")
        sys.exit(1)
