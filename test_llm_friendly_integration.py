#!/usr/bin/env python3
"""
Test the LLM-friendly IR_CONTEXT_REQUEST integration.
This tests that the actual aider codebase now generates compact, LLM-compatible packages.
"""

import os
import sys
import time

def test_llm_friendly_integration():
    """Test that the LLM-friendly IR_CONTEXT_REQUEST integration works in the actual codebase."""
    
    print("🧪 Testing LLM-Friendly IR_CONTEXT_REQUEST Integration")
    print("=" * 60)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)

        # Import the required modules from the actual aider codebase
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        print("✅ Successfully imported updated IR context request modules")
        
        # Create a context request handler
        project_path = "aider-main/aider/coders"  # Use the same path as the enhanced IR
        handler = ContextRequestHandler(project_path)

        # Load enhanced inheritance IR if available
        import json
        enhanced_ir_file = "final_enhanced_ir.json"
        if os.path.exists(enhanced_ir_file):
            print(f"🧬 Loading enhanced inheritance IR from {enhanced_ir_file}")
            with open(enhanced_ir_file, 'r') as f:
                enhanced_ir_data = json.load(f)
            handler._ir_cache = enhanced_ir_data  # Force use of enhanced IR
            print(f"✅ Enhanced inheritance IR loaded with inheritance analysis")
        else:
            print(f"⚠️  Enhanced IR not found, using standard IR generation")

        print(f"✅ Created ContextRequestHandler for project: {project_path}")
        
        # Test 1: LLM-friendly request (default behavior)
        print("\n🤖 Test 1: LLM-Friendly IR Context Request (Default)")
        
        llm_request = IRContextRequest(
            user_query="Show me class inheritance patterns and method overrides in the codebase",
            task_description="Analyze inheritance relationships, method overrides, and super() calls",
            task_type="inheritance_analysis",
            focus_entities=["UnknownEditFormat", "__init__", "MissingAPIKeyError", "Coder", "inheritance", "ValueError"],
            max_tokens=3000,
            include_ir_slices=True,
            include_code_context=True,
            # LLM-friendly options (should be default)
            llm_friendly=True,
            max_output_chars=30000,
            max_entities=12
        )
        
        start_time = time.time()
        result = handler.process_ir_context_request(llm_request)
        processing_time = time.time() - start_time
        
        print(f"✅ LLM-friendly request processed in {processing_time:.2f}s")
        
        # Check that LLM-friendly package was generated
        if "llm_friendly_package" in result:
            package_size = result["package_size_chars"]
            compatibility = result["llm_compatibility"]
            
            print(f"✅ LLM-friendly package generated!")
            print(f"   📦 Package size: {package_size:,} characters")
            print(f"   🤖 Compatibility: {compatibility}")
            
            # Verify it's actually LLM-compatible
            if package_size <= 32000:
                print("   ✅ Package is GPT-4 compatible!")
            elif package_size <= 100000:
                print("   ⚠️  Package requires GPT-4 Turbo or Claude")
            else:
                print("   ❌ Package is still too large for standard LLMs")
                
            # Save the LLM package to see what it looks like
            with open("test_llm_friendly_package_with_inheritance.txt", "w", encoding="utf-8") as f:
                f.write(result["llm_friendly_package"])
            print(f"   💾 LLM package with inheritance data saved to: test_llm_friendly_package_with_inheritance.txt")

            # Check for inheritance indicators in the package
            package_content = result["llm_friendly_package"]
            inheritance_indicators = [
                "Belongs to Class:",
                "Inherits From:",
                "🔁 Inheritance Context",
                "UnknownEditFormat",
                "ValueError",
                "calls_super",
                "super()",
                "(method)",
                "class_name"
            ]

            found_indicators = [ind for ind in inheritance_indicators if ind in package_content]
            print(f"   🧬 Inheritance indicators found: {len(found_indicators)}/{len(inheritance_indicators)}")
            for indicator in found_indicators[:5]:  # Show first 5
                print(f"      ✅ {indicator}")
            if len(found_indicators) > 5:
                print(f"      ... and {len(found_indicators) - 5} more")
            
        else:
            print("❌ LLM-friendly package was not generated!")
            return False
        
        # Test 2: Full package request (legacy behavior)
        print("\n📋 Test 2: Full IR Context Request (Legacy)")
        
        full_request = IRContextRequest(
            user_query="How does the intelligent context selection work?",
            task_description="Understand the context selection implementation",
            task_type="general_analysis",
            focus_entities=["context", "selection", "intelligent"],
            max_tokens=4000,
            include_ir_slices=True,
            include_code_context=True,
            # Disable LLM-friendly mode for comparison
            llm_friendly=False
        )
        
        start_time = time.time()
        full_result = handler.process_ir_context_request(full_request)
        full_processing_time = time.time() - start_time
        
        print(f"✅ Full request processed in {full_processing_time:.2f}s")
        
        # Compare sizes
        full_entities = len(full_result.get("ir_slices", []))
        llm_entities = len(result.get("ir_slices", []))
        
        print(f"\n📊 Comparison:")
        print(f"   Full package: {full_entities} entities")
        print(f"   LLM package: {llm_entities} entities")
        print(f"   Size reduction: {((full_entities - llm_entities) / full_entities * 100):.1f}%")
        
        # Test 3: Verify JSON request parsing works
        print("\n🔧 Test 3: JSON Request Format Compatibility")
        
        # Simulate what would come from base_coder.py
        request_json = {
            "user_query": "Find performance bottlenecks",
            "task_description": "Identify slow code paths",
            "task_type": "debugging",
            "focus_entities": ["performance", "bottleneck"],
            "max_tokens": 3000,
            "llm_friendly": True,
            "max_output_chars": 25000,
            "max_entities": 6
        }
        
        json_request = IRContextRequest(
            user_query=request_json.get("user_query"),
            task_description=request_json.get("task_description"),
            task_type=request_json.get("task_type", "general_analysis"),
            focus_entities=request_json.get("focus_entities", []),
            max_tokens=request_json.get("max_tokens", 8000),
            include_ir_slices=request_json.get("include_ir_slices", True),
            include_code_context=request_json.get("include_code_context", True),
            llm_friendly=request_json.get("llm_friendly", True),
            max_output_chars=request_json.get("max_output_chars", 30000),
            max_entities=request_json.get("max_entities", 10)
        )
        
        json_result = handler.process_ir_context_request(json_request)
        
        if "llm_friendly_package" in json_result:
            print("✅ JSON request format works correctly")
            print(f"   📦 Package size: {json_result['package_size_chars']:,} characters")
            print(f"   🤖 Compatibility: {json_result['llm_compatibility']}")
        else:
            print("❌ JSON request format failed")
            return False
        
        print(f"\n🎉 LLM-Friendly Integration Test Completed Successfully!")
        print(f"✅ All tests passed - the system now generates LLM-compatible packages by default")
        print(f"📄 Check test_llm_friendly_package.txt to see the compact output format")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure the aider.context_request module is available")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_llm_friendly_integration()
    if success:
        print("\n🚀 Integration successful! The aider codebase now supports LLM-friendly IR context packages.")
        sys.exit(0)
    else:
        print("\n❌ Integration test failed. Please check the implementation.")
        sys.exit(1)
