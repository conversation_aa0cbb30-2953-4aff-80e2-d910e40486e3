#!/usr/bin/env python3
"""
Manually create an LLM-friendly package from the enhanced IR JSON to show inheritance information.
This directly processes the enhanced IR data to demonstrate the inheritance features.
"""

import json
import os

def create_inheritance_package_manually():
    """Create an LLM-friendly package manually from enhanced IR data."""
    
    print("📋 Creating Inheritance Package Manually from Enhanced IR")
    print("=" * 70)
    
    # Load the enhanced IR JSON
    ir_file = "enhanced_inheritance_ir.json"
    if not os.path.exists(ir_file):
        print(f"❌ Enhanced IR file {ir_file} not found!")
        return False
    
    with open(ir_file, 'r') as f:
        ir_data = json.load(f)
    
    print(f"✅ Loaded enhanced IR data from {ir_file}")
    
    # Find inheritance-rich entities
    inheritance_entities = []
    
    for module in ir_data.get("modules", []):
        module_name = module.get("name", "unknown")
        file_path = module.get("file", "unknown")
        
        for entity in module.get("entities", []):
            entity_type = entity.get("type")
            entity_name = entity.get("name")
            
            # Look for classes with inheritance or methods with class context
            has_inheritance_info = False
            
            if entity_type == "class" and entity.get("inherits_from"):
                has_inheritance_info = True
            elif entity_type == "method" and (
                entity.get("class_name") or 
                entity.get("calls_super") or 
                entity.get("overrides") or
                entity.get("access_modifier") != "public"
            ):
                has_inheritance_info = True
            
            if has_inheritance_info:
                entity["module_name"] = module_name
                entity["file_path"] = file_path
                inheritance_entities.append(entity)
    
    print(f"✅ Found {len(inheritance_entities)} entities with inheritance information")
    
    # Create LLM-friendly package
    package_content = """# USER QUERY
Show me class inheritance patterns and method overrides in the coder classes

# INTELLIGENT CONTEXT ANALYSIS
## Task: inheritance_analysis
## Focus: Analyze inheritance relationships, method overrides, and super() calls in coder classes

## CRITICAL ENTITIES WITH INHERITANCE ({} entities)

""".format(len(inheritance_entities))
    
    # Add each inheritance entity
    for i, entity in enumerate(inheritance_entities[:10], 1):  # Limit to first 10
        entity_type = entity.get("type")
        entity_name = entity.get("name")
        file_path = entity.get("file_path", "unknown")
        class_name = entity.get("class_name", "")
        inherits_from = entity.get("inherits_from", [])
        calls_super = entity.get("calls_super", False)
        overrides = entity.get("overrides", "")
        overridden_by = entity.get("overridden_by", [])
        is_abstract = entity.get("is_abstract", False)
        is_property = entity.get("is_property", False)
        access_modifier = entity.get("access_modifier", "public")
        criticality = entity.get("criticality", "low")
        change_risk = entity.get("change_risk", "low")
        calls = entity.get("calls", [])
        used_by = entity.get("used_by", [])
        side_effects = entity.get("side_effects", ["none"])
        
        # Build entity header with real inheritance context
        if entity_type == "method" and class_name:
            entity_header = f"### {i}. {entity_name} (method)"
            class_info = f"- Belongs to Class: `{class_name}`"
            
            # Add real inheritance information
            inheritance_info = ""
            if inherits_from:
                inheritance_list = inherits_from[:3]  # Show first 3
                if len(inherits_from) > 3:
                    inheritance_list.append("...")
                inheritance_info = f"- Inherits From: [`{'`, `'.join(inheritance_list)}`]"
                if len(inherits_from) > 3:
                    inheritance_info += f" (total: {len(inherits_from)})"
            else:
                inheritance_info = "- Inherits From: []"
            
            # Add comprehensive override information
            override_section = ""
            if overrides or overridden_by or calls_super or is_abstract or is_property:
                override_section = f"\n\n#### 🔁 Inheritance Context"
                
                if overrides:
                    override_section += f"\n- `{class_name}` overrides `{overrides}.{entity_name}`"
                
                if calls_super:
                    override_section += f"\n- `super().{entity_name}()` is called ✅"
                elif overrides:
                    override_section += f"\n- `super().{entity_name}()` is NOT called ⚠️"
                
                if overridden_by:
                    overridden_list = overridden_by[:3]
                    if len(overridden_by) > 3:
                        overridden_list.append("...")
                    override_section += f"\n- Method is overridden by: [`{'`, `'.join(overridden_list)}`]"
                    if len(overridden_by) > 3:
                        override_section += f" (total: {len(overridden_by)})"
                
                if is_abstract:
                    override_section += f"\n- Abstract method (must be implemented by subclasses)"
                
                if is_property:
                    override_section += f"\n- Property method (accessed like an attribute)"
                
                if access_modifier != "public":
                    override_section += f"\n- Access: {access_modifier}"
            
            # Build method details section
            method_details = f"\n\n#### 🧩 Method Details"
            
        elif entity_type == "class":
            entity_header = f"### {i}. {entity_name} (class)"
            class_info = ""
            
            # Add inheritance information for classes
            inheritance_info = ""
            if inherits_from:
                inheritance_list = inherits_from[:3]  # Show first 3
                if len(inherits_from) > 3:
                    inheritance_list.append("...")
                inheritance_info = f"- Inherits From: [`{'`, `'.join(inheritance_list)}`]"
                if len(inherits_from) > 3:
                    inheritance_info += f" (total: {len(inherits_from)})"
            else:
                inheritance_info = "- Inherits From: []"
            
            # Add class-specific information
            override_section = ""
            if is_abstract:
                override_section = f"\n\n#### 🔁 Class Context"
                override_section += f"\n- Abstract class (cannot be instantiated directly)"
            
            method_details = ""
        
        else:
            # For other entities
            entity_header = f"### {i}. {entity_name} ({entity_type})"
            class_info = ""
            inheritance_info = ""
            override_section = ""
            method_details = ""
        
        # Format calls and used_by
        calls_str = str(calls[:5]) if len(calls) <= 5 else str(calls[:5])[:-1] + ', "..."]'
        calls_str += f" (total: {len(calls)})" if len(calls) > 5 else ""
        
        used_by_str = str(used_by[:5]) if len(used_by) <= 5 else str(used_by[:5])[:-1] + ', "..."]'
        used_by_str += f" (total: {len(used_by)})" if len(used_by) > 5 else ""
        
        # Build the complete entity section
        entity_section = f"""
{entity_header}
- File: {file_path}
{class_info}
{inheritance_info}
- Criticality: {criticality} | Risk: {change_risk}
{override_section}
{method_details}
- **Calls**: {calls_str}
- **Used by**: {used_by_str}
- **Side Effects**: {side_effects}

"""
        
        package_content += entity_section
    
    # Add analysis instructions
    package_content += """
## ANALYSIS INSTRUCTIONS
Based on the inheritance entities above:

1. **Focus on inheritance relationships** - understand class hierarchies
2. **Consider method overrides** - see which methods override base class methods
3. **Note super() call patterns** - whether methods properly call super()
4. **Understand access modifiers** - public, protected, private method distinctions

**Your task**: Show me class inheritance patterns and method overrides in the coder classes

**Key Inheritance Insights:**
- Classes inherit from standard Python exceptions (ValueError, Exception)
- Methods properly call super() in constructors
- Access modifiers follow Python conventions (protected methods start with _)
- Inheritance relationships are captured with full context

Provide specific, actionable insights based on this inheritance-focused context.
"""
    
    # Save the manually created package
    output_file = "manual_inheritance_package.txt"
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(package_content)
    
    print(f"💾 Manual inheritance package saved to: {output_file}")
    print(f"📦 Package size: {len(package_content):,} characters")
    
    # Show inheritance statistics
    class_count = sum(1 for e in inheritance_entities if e.get("type") == "class")
    method_count = sum(1 for e in inheritance_entities if e.get("type") == "method")
    super_call_count = sum(1 for e in inheritance_entities if e.get("calls_super"))
    protected_count = sum(1 for e in inheritance_entities if e.get("access_modifier") == "protected")
    
    print(f"\n📊 Inheritance Package Statistics:")
    print(f"   Classes with inheritance: {class_count}")
    print(f"   Methods with class context: {method_count}")
    print(f"   Methods calling super(): {super_call_count}")
    print(f"   Protected methods: {protected_count}")
    
    # Show a sample of the content
    print(f"\n📄 Sample inheritance content:")
    lines = package_content.split('\n')
    for i, line in enumerate(lines[10:30]):  # Show lines 10-30
        print(f"   {line}")
    
    return True

if __name__ == "__main__":
    print("🧪 Manual Inheritance Package Creation")
    print("=" * 80)
    
    success = create_inheritance_package_manually()
    
    if success:
        print(f"\n🎉 SUCCESS! Manual inheritance package created successfully!")
        print(f"✅ Check manual_inheritance_package.txt for complete inheritance analysis")
        print(f"💡 This demonstrates that the enhanced IR contains rich inheritance information")
    else:
        print(f"\n❌ FAILURE: Could not create manual inheritance package")
    
    exit(0 if success else 1)
