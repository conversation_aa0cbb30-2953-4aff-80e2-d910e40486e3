# USER QUERY
Show me class inheritance patterns and method overrides in the codebase

# INTELLIGENT CONTEXT ANALYSIS
## Task: inheritance_analysis
## Focus: Analyze inheritance relationships, method overrides, and super() calls

## CRITICAL ENTITIES (12 most important)

### 1. __init__ (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 2. __init__ (method)
- File: base_coder_old.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 3. __init__ (method)
- File: search_replace.py
- Belongs to Class: `RelativeIndenter`
- Inherits From: []
- Criticality: low | Risk: low

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 4. UnknownEditFormat (class)
- File: base_coder.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 5. MissingAPIKeyError (class)
- File: base_coder.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 6. UnknownEditFormat (class)
- File: base_coder_old.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 7. MissingAPIKeyError (class)
- File: base_coder_old.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 8. _initialize_ir_preloading (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 9. _stop_waiting_spinner (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 10. __del__ (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 11. _extract_actual_user_query (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 12. _contains_map_request_content (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

## KEY IMPLEMENTATIONS (12 functions)

### 1. __init__
```python
    def __init__(self, edit_format, valid_formats):
        self.edit_format = edit_format
        self.valid_formats = valid_formats
        super().__init__(
            f"Unknown edit format {edit_format}. Valid formats are: {', '.join(valid_formats)}"
        )


```

### 2. __init__
```python
    def __init__(
        self,
        main_model,
        io,
        repo=None,
        fnames=None,
        read_only_fnames=None,
        show_diffs=False,
        auto_commits=True,
        dirty_commits=True,
        dry_run=True,  # Force dry_run to True to prevent file modifications
        map_tokens=1024,
        verbose=False,
        stream=True,
        use_git=True,
        cur_messages=None,
        done_messages=None,
        restore_chat_history=False,
        auto_lint=True,
        auto_test=False,
        lint_cmds=None,
        test_cmd=None,
        aider_commit_hashes=None,
        map_mul_no_files=8,
        commands=None,
        summarizer=None,
        total_cost=0.0,
        analytics=None,
        map_refresh="auto",
        cache_prompts=False,
        num_cache_warming_pings=0,
    # ... (implementation continues)
```

### 3. __init__
```python
    def __init__(self, texts):
        """
        Based on the texts, choose a unicode character that isn't in any of them.
        """

        chars = set()
        for text in texts:
            chars.update(text)

        ARROW = "←"
        if ARROW not in chars:
            self.marker = ARROW
        else:
            self.marker = self.select_unique_marker(chars)

```

### 4. UnknownEditFormat
```python
class UnknownEditFormat(ValueError):
```

### 5. MissingAPIKeyError
```python
class MissingAPIKeyError(ValueError):
    pass


```

### 6. UnknownEditFormat
```python
class UnknownEditFormat(ValueError):
```

### 7. MissingAPIKeyError
```python
class MissingAPIKeyError(ValueError):
    pass


```

### 8. _initialize_ir_preloading
```python
    def _initialize_ir_preloading(self, project_path: str):
        """
        Initialize IR preloading for better performance.
        This runs in the background to cache IR data for faster context requests.

        Args:
            project_path: Path to the project root
        """
        if not CONTEXT_REQUEST_AVAILABLE:
            return

        try:
            from ..context_request import ContextRequestHandler
            import threading

```

### 9. _stop_waiting_spinner
```python
    def _stop_waiting_spinner(self):
        """Stop and clear the waiting spinner if it is running."""
        spinner = getattr(self, "waiting_spinner", None)
        if spinner:
            try:
                spinner.stop()
            finally:
                self.waiting_spinner = None

```

### 10. __del__
```python
    def __del__(self):
        """Cleanup when the Coder object is destroyed."""
        self.ok_to_warm_cache = False

```

### 11. _extract_actual_user_query
```python
    def _extract_actual_user_query(self, user_message):
        """
        Extract the actual user query from a message that might contain MAP_REQUEST content.

        Args:
            user_message: The user message that might contain MAP_REQUEST content

        Returns:
            The actual user query without MAP_REQUEST content
        """
        import re

        # Look for patterns that indicate the original query
        patterns = [
            r"Now please answer the original user query:\s*(.+?)(?:\n|$)",
            r"original user query:\s*(.+?)(?:\n|$)",
            r"user query:\s*(.+?)(?:\n|$)",
            r"query:\s*(.+?)(?:\n|$)"
        ]

        for pattern in patterns:
            match = re.search(pattern, user_message, re.IGNORECASE)
            if match:
    # ... (implementation continues)
```

### 12. _contains_map_request_content
```python
    def _contains_map_request_content(self, content):
        """
        Check if content contains MAP_REQUEST related content that should be excluded.

        Args:
            content: The content to check

        Returns:
            True if content contains MAP_REQUEST content, False otherwise
        """
        if not content:
            return False

        map_request_indicators = [
            "Based on your map request",
            "Focused Repository Map",
            "Search Keywords:",
            "Files Found:",
            "CRITICAL INSTRUCTION: This map shows file structure",
            "Your NEXT response must be EXACTLY this format",
            "CONTEXT_REQUEST:",
            "Repository Structure",
            "Search Results"
        ]

        for indicator in map_request_indicators:
    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 12 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: Show me class inheritance patterns and method overrides in the codebase

Provide specific, actionable insights based on this focused context.
