# USER QUERY
Why is my context selection taking so long?

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Debug performance issues in context selection

## CRITICAL ENTITIES (12 most important)

### 1. _display_ir_context_response_to_user (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🔁 Inheritance Context
- Access: protected
- Part of core `Coder` class hierarchy
- Inherits base functionality and patterns

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 2. _create_ir_context_augmented_prompt (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🔁 Inheritance Context
- Access: protected
- Part of core `Coder` class hierarchy
- Inherits base functionality and patterns

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 3. __init__ (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🔁 Inheritance Context
- Constructor method - check implementation for super() calls
- Access: protected
- Part of core `Coder` class hierarchy
- Inherits base functionality and patterns

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 4. __init__ (method)
- File: base_coder_old.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🔁 Inheritance Context
- Constructor method - check implementation for super() calls
- Access: protected
- Part of core `Coder` class hierarchy
- Inherits base functionality and patterns

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 5. __init__ (method)
- File: search_replace.py
- Belongs to Class: `RelativeIndenter`
- Inherits From: []
- Criticality: low | Risk: low

#### 🔁 Inheritance Context
- Constructor method - check implementation for super() calls
- Access: protected

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 6. UnknownEditFormat (class)
- File: base_coder.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 7. MissingAPIKeyError (class)
- File: base_coder.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 8. UnknownEditFormat (class)
- File: base_coder_old.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 9. MissingAPIKeyError (class)
- File: base_coder_old.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 10. copy_context (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low

#### 🔁 Inheritance Context
- Part of core `Coder` class hierarchy
- Inherits base functionality and patterns

#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 11. process_context_requests (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: high | Risk: high

#### 🔁 Inheritance Context
- Performance-critical method for context processing
- Part of `Coder` class architecture

#### 🧩 Method Details
- **Calls**: ["tool_warning", "getcwd", "basename", "exists", "join", "..."] (total: 15)
- **Used by**: ["test_full_aider_integration", "test_aider_coder_path_fix", "test_llm_workflow_understanding", "test_repo_map_compatibility", "test_aider_context_request", "..."] (total: 7)
- **Side Effects**: writes_log, network_io, database_io

### 12. process_ir_context_requests (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: high | Risk: high

#### 🔁 Inheritance Context
- Performance-critical method for context processing
- Part of `Coder` class architecture

#### 🧩 Method Details
- **Calls**: ["re.search", "json.loads", "IRContextRequest", "process_ir_context_request", "ContextRequestHandler", "..."] (total: 10)
- **Used by**: ["process_context_requests", "send_message", "run_one", "test_ir_context_integration"] (total: 4)
- **Side Effects**: network_io, writes_log, database_io

## KEY IMPLEMENTATIONS (12 functions)

### 1. _display_ir_context_response_to_user
```python
    def _display_ir_context_response_to_user(self, result, ir_request):
        """
        Display the IR_CONTEXT_REQUEST response to the user in the chat.

        Args:
            result: The IR context result
            ir_request: The original IR context request
        """
        if not result:
            return

        # Create a user-friendly header
        header = f"# 🧠 IR_CONTEXT_REQUEST Response\n\n"
        header += f"**Task**: {ir_request.task_description}  \n"
        header += f"**Type**: {ir_request.task_type}  \n"
        header += f"**Max Tokens**: {ir_request.max_tokens}  \n"
        header += f"**Focus Entities**: {', '.join(ir_request.focus_entities) if ir_request.focus_entities else 'None'}  \n\n"

        # Add summary statistics
        summary = result.get("summary", {})
    # ... (implementation continues)
```

### 2. _create_ir_context_augmented_prompt
```python
    def _create_ir_context_augmented_prompt(self, result, user_message):
        """
        Create an augmented prompt for the LLM based on the IR context result.

        Args:
            result: The IR context result
            user_message: The original user message

        Returns:
            Augmented prompt string
        """
        # Use LLM-friendly package if available, otherwise use full context
        if "llm_friendly_package" in result:
            # Return the compact, LLM-optimized package directly
            return result["llm_friendly_package"]

        # Fallback to original format for backward compatibility
        prompt = f"""
# Intelligent Context Analysis

    # ... (implementation continues)
```

### 3. __init__
```python
    def __init__(self, edit_format, valid_formats):
        self.edit_format = edit_format
        self.valid_formats = valid_formats
        super().__init__(
            f"Unknown edit format {edit_format}. Valid formats are: {', '.join(valid_formats)}"
        )


```

### 4. __init__
```python
    def __init__(
        self,
        main_model,
        io,
        repo=None,
        fnames=None,
        read_only_fnames=None,
        show_diffs=False,
        auto_commits=True,
        dirty_commits=True,
        dry_run=True,  # Force dry_run to True to prevent file modifications
        map_tokens=1024,
        verbose=False,
        stream=True,
        use_git=True,
        cur_messages=None,
        done_messages=None,
        restore_chat_history=False,
        auto_lint=True,
        auto_test=False,
        lint_cmds=None,
        test_cmd=None,
        aider_commit_hashes=None,
        map_mul_no_files=8,
        commands=None,
        summarizer=None,
        total_cost=0.0,
        analytics=None,
        map_refresh="auto",
        cache_prompts=False,
        num_cache_warming_pings=0,
    # ... (implementation continues)
```

### 5. __init__
```python
    def __init__(self, texts):
        """
        Based on the texts, choose a unicode character that isn't in any of them.
        """

        chars = set()
        for text in texts:
            chars.update(text)

        ARROW = "←"
        if ARROW not in chars:
            self.marker = ARROW
        else:
            self.marker = self.select_unique_marker(chars)

```

### 6. UnknownEditFormat
```python
class UnknownEditFormat(ValueError):
```

### 7. MissingAPIKeyError
```python
class MissingAPIKeyError(ValueError):
    pass


```

### 8. UnknownEditFormat
```python
class UnknownEditFormat(ValueError):
```

### 9. MissingAPIKeyError
```python
class MissingAPIKeyError(ValueError):
    pass


```

### 10. copy_context
```python
    def copy_context(self):
        if self.auto_copy_context:
            self.commands.cmd_copy_context()

```

### 11. process_context_requests
```python
    def process_context_requests(self, content, user_message):
        """
        Process any context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if a context request was detected,
            or (content, None) if no context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Initialize context_request_integration if not already done
    # ... (implementation continues)
```

### 12. process_ir_context_requests
```python
    def process_ir_context_requests(self, content, user_message):
        """
        Process any IR context requests in the content.

        Args:
            content: The LLM response content
            user_message: The original user message

        Returns:
            A tuple of (cleaned_content, augmented_prompt) if an IR context request was detected,
            or (content, None) if no IR context request was detected
        """
        import re
        import json

        # Check if CONTEXT_REQUEST is available
        if not CONTEXT_REQUEST_AVAILABLE:
            self.io.tool_warning("IR_CONTEXT_REQUEST functionality is not available. Please install the required modules.")
            return content, None

        # Look for IR_CONTEXT_REQUEST pattern
        patterns = [
    # ... (implementation continues)
```

## ANALYSIS INSTRUCTIONS
Based on the 12 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: Why is my context selection taking so long?

Provide specific, actionable insights based on this focused context.
