# USER QUERY
Show me class inheritance patterns and method overrides in the codebase

# INTELLIGENT CONTEXT ANALYSIS
## Task: inheritance_analysis
## Focus: Analyze inheritance relationships, method overrides, and super() calls

## CRITICAL ENTITIES WITH INHERITANCE (10 entities)


### 1. __all__ (variable)
- File: __init__.py


- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 2. UnknownEditFormat (class)
- File: base_coder.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 3. __init__ (method)
- File: base_coder.py
- Belongs to Class: `UnknownEditFormat`
- Inherits From: [`ValueError`]
- Criticality: low | Risk: low


#### 🔁 Inheritance Context
- `super().__init__()` is called ✅
- Access: protected


#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 4. MissingAPIKeyError (class)
- File: base_coder.py

- Inherits From: [`ValueError`]
- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 5. FinishReasonLength (class)
- File: base_coder.py

- Inherits From: [`Exception`]
- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 6. wrap_fence (function)
- File: base_coder.py


- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 7. all_fences (variable)
- File: base_coder.py


- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 8. Coder (class)
- File: base_coder.py

- Inherits From: []
- Criticality: low | Risk: low


- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 9. create (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low



#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


### 10. clone (method)
- File: base_coder.py
- Belongs to Class: `Coder`
- Inherits From: []
- Criticality: low | Risk: low



#### 🧩 Method Details
- **Calls**: []
- **Used by**: []
- **Side Effects**: ['none']


## ANALYSIS INSTRUCTIONS
Based on the inheritance entities above:

1. **Focus on inheritance relationships** - understand class hierarchies
2. **Consider method overrides** - see which methods override base class methods
3. **Note super() call patterns** - whether methods properly call super()
4. **Understand access modifiers** - public, protected, private method distinctions

**Your task**: Show me class inheritance patterns and method overrides in the codebase

**Key Inheritance Insights:**
- Classes inherit from standard Python exceptions (ValueError, Exception)
- Methods properly call super() in constructors where detected
- Access modifiers follow Python conventions (protected methods start with _)
- Inheritance relationships are captured with full context

Provide specific, actionable insights based on this inheritance-focused context.
