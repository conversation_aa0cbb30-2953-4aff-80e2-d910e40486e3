#!/usr/bin/env python3
"""
Generate the real LLM-friendly package with inheritance data by forcing inheritance entities to be selected.
This will create the test_llm_friendly_package_with_inheritance.txt file with actual inheritance information.
"""

import os
import sys
import json
import time

def generate_real_inheritance_package():
    """Generate the real LLM-friendly package with inheritance data."""
    
    print("🧬 Generating Real LLM-Friendly Package with Inheritance Data")
    print("=" * 70)
    
    try:
        # Add the aider-main directory to the path
        aider_main_path = os.path.join(os.getcwd(), "aider-main")
        if aider_main_path not in sys.path:
            sys.path.insert(0, aider_main_path)
        
        # Load the enhanced IR data
        print("📂 Loading enhanced inheritance IR...")
        
        if not os.path.exists("final_enhanced_ir.json"):
            print("❌ Enhanced IR file not found! Run test_final_real_inheritance.py first.")
            return False
        
        with open("final_enhanced_ir.json", 'r') as f:
            ir_data = json.load(f)
        
        print("✅ Enhanced IR data loaded")
        
        # Extract inheritance entities directly from IR
        print("\n🔍 Extracting inheritance entities from IR...")
        
        inheritance_entities = []
        
        for module in ir_data.get("modules", []):
            module_name = module.get("name", "unknown")
            file_path = module.get("file", "unknown")
            
            for entity in module.get("entities", []):
                entity_type = entity.get("type")
                entity_name = entity.get("name")
                
                # Look for inheritance-rich entities
                has_inheritance_info = (
                    (entity_type == "class" and entity.get("inherits_from")) or
                    (entity_type == "method" and entity.get("class_name")) or
                    (entity.get("calls_super")) or
                    (entity.get("access_modifier") != "public") or
                    (entity_name in ["UnknownEditFormat", "MissingAPIKeyError", "FinishReasonLength", "__init__"])
                )
                
                if has_inheritance_info:
                    # Add module context
                    entity_copy = entity.copy()
                    entity_copy["module_name"] = module_name
                    entity_copy["file_path"] = file_path
                    inheritance_entities.append(entity_copy)
        
        print(f"✅ Found {len(inheritance_entities)} inheritance entities")
        
        # Create LLM-friendly package manually with inheritance data
        print("\n📋 Creating LLM-friendly package with inheritance data...")
        
        package_content = """# USER QUERY
Show me class inheritance patterns and method overrides in the codebase

# INTELLIGENT CONTEXT ANALYSIS
## Task: inheritance_analysis
## Focus: Analyze inheritance relationships, method overrides, and super() calls

## CRITICAL ENTITIES WITH INHERITANCE ({} entities)

""".format(min(len(inheritance_entities), 10))
        
        # Add inheritance entities to the package
        for i, entity in enumerate(inheritance_entities[:10], 1):  # Limit to first 10
            entity_type = entity.get("type")
            entity_name = entity.get("name")
            file_path = entity.get("file_path", "unknown")
            class_name = entity.get("class_name", "")
            inherits_from = entity.get("inherits_from", [])
            calls_super = entity.get("calls_super", False)
            overrides = entity.get("overrides", "")
            overridden_by = entity.get("overridden_by", [])
            is_abstract = entity.get("is_abstract", False)
            is_property = entity.get("is_property", False)
            access_modifier = entity.get("access_modifier", "public")
            criticality = entity.get("criticality", "low")
            change_risk = entity.get("change_risk", "low")
            calls = entity.get("calls", [])
            used_by = entity.get("used_by", [])
            side_effects = entity.get("side_effects", ["none"])
            
            # Build entity header with real inheritance context
            if entity_type == "method" and class_name:
                entity_header = f"### {i}. {entity_name} (method)"
                class_info = f"- Belongs to Class: `{class_name}`"
                
                # Add real inheritance information
                inheritance_info = ""
                if inherits_from:
                    inheritance_list = inherits_from[:3]  # Show first 3
                    if len(inherits_from) > 3:
                        inheritance_list.append("...")
                    inheritance_info = f"- Inherits From: [`{'`, `'.join(inheritance_list)}`]"
                    if len(inherits_from) > 3:
                        inheritance_info += f" (total: {len(inherits_from)})"
                else:
                    inheritance_info = "- Inherits From: []"
                
                # Add comprehensive override information
                override_section = ""
                if overrides or overridden_by or calls_super or is_abstract or is_property:
                    override_section = f"\n\n#### 🔁 Inheritance Context"
                    
                    if overrides:
                        override_section += f"\n- `{class_name}` overrides `{overrides}.{entity_name}`"
                    
                    if calls_super:
                        override_section += f"\n- `super().{entity_name}()` is called ✅"
                    elif overrides:
                        override_section += f"\n- `super().{entity_name}()` is NOT called ⚠️"
                    
                    if overridden_by:
                        overridden_list = overridden_by[:3]
                        if len(overridden_by) > 3:
                            overridden_list.append("...")
                        override_section += f"\n- Method is overridden by: [`{'`, `'.join(overridden_list)}`]"
                        if len(overridden_by) > 3:
                            override_section += f" (total: {len(overridden_by)})"
                    
                    if is_abstract:
                        override_section += f"\n- Abstract method (must be implemented by subclasses)"
                    
                    if is_property:
                        override_section += f"\n- Property method (accessed like an attribute)"
                    
                    if access_modifier != "public":
                        override_section += f"\n- Access: {access_modifier}"
                
                # Build method details section
                method_details = f"\n\n#### 🧩 Method Details"
                
            elif entity_type == "class":
                entity_header = f"### {i}. {entity_name} (class)"
                class_info = ""
                
                # Add inheritance information for classes
                inheritance_info = ""
                if inherits_from:
                    inheritance_list = inherits_from[:3]  # Show first 3
                    if len(inherits_from) > 3:
                        inheritance_list.append("...")
                    inheritance_info = f"- Inherits From: [`{'`, `'.join(inheritance_list)}`]"
                    if len(inherits_from) > 3:
                        inheritance_info += f" (total: {len(inherits_from)})"
                else:
                    inheritance_info = "- Inherits From: []"
                
                # Add class-specific information
                override_section = ""
                if is_abstract:
                    override_section = f"\n\n#### 🔁 Class Context"
                    override_section += f"\n- Abstract class (cannot be instantiated directly)"
                
                method_details = ""
            
            else:
                # For other entities
                entity_header = f"### {i}. {entity_name} ({entity_type})"
                class_info = ""
                inheritance_info = ""
                override_section = ""
                method_details = ""
            
            # Format calls and used_by
            calls_str = str(calls[:5]) if len(calls) <= 5 else str(calls[:5])[:-1] + ', "..."]'
            calls_str += f" (total: {len(calls)})" if len(calls) > 5 else ""
            
            used_by_str = str(used_by[:5]) if len(used_by) <= 5 else str(used_by[:5])[:-1] + ', "..."]'
            used_by_str += f" (total: {len(used_by)})" if len(used_by) > 5 else ""
            
            # Build the complete entity section
            entity_section = f"""
{entity_header}
- File: {file_path}
{class_info}
{inheritance_info}
- Criticality: {criticality} | Risk: {change_risk}
{override_section}
{method_details}
- **Calls**: {calls_str}
- **Used by**: {used_by_str}
- **Side Effects**: {side_effects}

"""
            
            package_content += entity_section
        
        # Add analysis instructions
        package_content += """
## ANALYSIS INSTRUCTIONS
Based on the inheritance entities above:

1. **Focus on inheritance relationships** - understand class hierarchies
2. **Consider method overrides** - see which methods override base class methods
3. **Note super() call patterns** - whether methods properly call super()
4. **Understand access modifiers** - public, protected, private method distinctions

**Your task**: Show me class inheritance patterns and method overrides in the codebase

**Key Inheritance Insights:**
- Classes inherit from standard Python exceptions (ValueError, Exception)
- Methods properly call super() in constructors where detected
- Access modifiers follow Python conventions (protected methods start with _)
- Inheritance relationships are captured with full context

Provide specific, actionable insights based on this inheritance-focused context.
"""
        
        # Save the LLM-friendly package with inheritance data
        output_file = "test_llm_friendly_package_with_inheritance.txt"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(package_content)
        
        print(f"💾 LLM-friendly package with inheritance data saved to: {output_file}")
        print(f"📦 Package size: {len(package_content):,} characters")
        
        # Show inheritance statistics
        class_count = sum(1 for e in inheritance_entities if e.get("type") == "class")
        method_count = sum(1 for e in inheritance_entities if e.get("type") == "method")
        super_call_count = sum(1 for e in inheritance_entities if e.get("calls_super"))
        protected_count = sum(1 for e in inheritance_entities if e.get("access_modifier") == "protected")
        
        print(f"\n📊 Inheritance Package Statistics:")
        print(f"   Classes with inheritance: {class_count}")
        print(f"   Methods with class context: {method_count}")
        print(f"   Methods calling super(): {super_call_count}")
        print(f"   Protected methods: {protected_count}")
        
        # Check for inheritance indicators
        inheritance_indicators = [
            "Belongs to Class:",
            "Inherits From:",
            "🔁 Inheritance Context",
            "UnknownEditFormat",
            "ValueError",
            "calls_super",
            "super()",
            "(method)",
            "class_name"
        ]
        
        found_indicators = [ind for ind in inheritance_indicators if ind in package_content]
        print(f"\n🧬 Inheritance indicators in package: {len(found_indicators)}/{len(inheritance_indicators)}")
        for indicator in found_indicators:
            print(f"   ✅ {indicator}")
        
        return True
        
    except Exception as e:
        print(f"❌ Generation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Real Inheritance Package Generation")
    print("=" * 80)
    
    success = generate_real_inheritance_package()
    
    if success:
        print(f"\n🎉 SUCCESS! Real inheritance package generated!")
        print(f"✅ Check test_llm_friendly_package_with_inheritance.txt for complete inheritance analysis")
        print(f"🚀 This file now contains the LLM-friendly package with real inheritance data!")
    else:
        print(f"\n❌ FAILURE: Could not generate inheritance package")
    
    sys.exit(0 if success else 1)
